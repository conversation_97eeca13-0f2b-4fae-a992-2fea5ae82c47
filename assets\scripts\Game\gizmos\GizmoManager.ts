import { _decorator, Component, Graphics, Node, find, Color } from 'cc';
import { EDITOR } from 'cc/env';
import { GizmoDrawer, autoRegisterGizmoDrawers } from './GizmoDrawer';

// Import gizmo classes to ensure their decorators are executed
import './EmitterArcGizmo';

const { ccclass, property, executeInEditMode } = _decorator;

/**
 * Global gizmo manager that handles all gizmo drawing
 * Should be attached to a global node in the scene
 */
@ccclass('GizmoManager')
@executeInEditMode(true)
export class GizmoManager extends Component {
    
    @property
    public gizmosEnabled: boolean = true;
    
    @property
    public drawInPlayMode: boolean = false;
    
    @property
    public refreshRate: number = 60; // FPS for gizmo updates
    
    @property
    public maxDrawDistance: number = 2000; // Maximum distance to draw gizmos
    
    // Graphics component for drawing
    private graphics: Graphics | null = null;
    
    // Registered gizmo drawers
    private static drawers: Map<string, GizmoDrawer> = new Map();
    
    // Update timer
    private updateTimer: number = 0;
    private updateInterval: number = 1 / 60; // Default 60 FPS
    
    // Singleton instance
    private static instance: GizmoManager | null = null;
    
    protected onLoad(): void {
        if (!EDITOR && !this.drawInPlayMode) return;

        // Set as singleton instance
        GizmoManager.instance = this;

        // Get or create Graphics component
        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);

        // Update refresh interval
        this.updateInterval = 1 / this.refreshRate;

        // Auto-register all decorated gizmo drawers
        GizmoManager.autoRegisterDrawers();

        console.log('GizmoManager: Initialized with auto-registered drawers');
    }
    
    protected onDestroy(): void {
        if (GizmoManager.instance === this) {
            GizmoManager.instance = null;
        }
    }
    
    protected update(deltaTime: number): void {
        if (!EDITOR && !this.drawInPlayMode) return;
        if (!this.graphics) return;

        // Throttle updates based on refresh rate
        this.updateTimer += deltaTime;
        if (this.updateTimer < this.updateInterval) return;
        this.updateTimer = 0;

        if (this.gizmosEnabled) {
            this.drawAllGizmos();
        } else {
            // Clear graphics when gizmos are disabled
            this.graphics.clear();
        }
    }
    
    /**
     * Draw all gizmos for all registered drawers
     */
    private drawAllGizmos(): void {
        if (!this.graphics) 
            return;
        
        // Clear previous drawings
        this.graphics.clear();
        
        // Get all drawers sorted by priority
        const sortedDrawers = Array.from(GizmoManager.drawers.values())
            .filter(drawer => drawer.enabled)
            .sort((a, b) => a.getPriority() - b.getPriority());
        
        // Find all nodes in the scene
        const rootNodes = this.findAllRootNodes();
        
        // Draw gizmos for each drawer
        for (const drawer of sortedDrawers) {
            this.drawGizmosForDrawer(drawer, rootNodes);
        }
    }
    
    /**
     * Draw gizmos for a specific drawer
     */
    private drawGizmosForDrawer(drawer: GizmoDrawer, rootNodes: Node[]): void {
        if (!this.graphics) return;
        
        const componentsToProcess: { component: Component, node: Node }[] = [];
        
        // Find all components of the drawer's type
        for (const rootNode of rootNodes) {
            this.findComponentsRecursive(rootNode, drawer, componentsToProcess);
        }
        
        // Draw gizmos for each component
        for (const { component, node } of componentsToProcess) {
            try {
                drawer.drawGizmos(component, this.graphics, node);
            } catch (error) {
                console.error(`GizmoManager: Error drawing gizmos for ${drawer.drawerName}:`, error);
            }
        }
    }
    
    /**
     * Recursively find components that match the drawer's type
     */
    private findComponentsRecursive(node: Node, drawer: GizmoDrawer, results: { component: Component, node: Node }[]): void {
        // Check distance from gizmo manager
        const distance = node.worldPosition.subtract(this.node.worldPosition).length();
        if (distance > this.maxDrawDistance) return;
        
        // Check components on this node
        const components = node.getComponents(Component);
        for (const component of components) {
            if (drawer.canHandle(component)) {
                results.push({ component, node });
            }
        }
        
        // Recursively check children
        for (const child of node.children) {
            this.findComponentsRecursive(child, drawer, results);
        }
    }
    
    /**
     * Find all root nodes in the scene
     */
    private findAllRootNodes(): Node[] {
        const scene = this.node.scene;
        if (!scene) return [];
        
        return scene.children.filter(child => child !== this.node);
    }
    
    /**
     * Register a gizmo drawer
     */
    public static registerDrawer(drawer: GizmoDrawer): void {
        const key = drawer.drawerName;
        
        if (GizmoManager.drawers.has(key)) {
            console.warn(`GizmoManager: Drawer ${key} is already registered`);
            return;
        }
        
        GizmoManager.drawers.set(key, drawer);
        drawer.onRegister();
        
        if (EDITOR) {
            console.log(`GizmoManager: Registered drawer ${key}`);
        }
    }
    
    /**
     * Unregister a gizmo drawer
     */
    public static unregisterDrawer(drawerName: string): boolean {
        const drawer = GizmoManager.drawers.get(drawerName);
        if (!drawer) return false;
        
        drawer.onUnregister();
        GizmoManager.drawers.delete(drawerName);
        
        if (EDITOR) {
            console.log(`GizmoManager: Unregistered drawer ${drawerName}`);
        }
        
        return true;
    }
    
    /**
     * Get a registered drawer by name
     */
    public static getDrawer(drawerName: string): GizmoDrawer | null {
        return GizmoManager.drawers.get(drawerName) || null;
    }
    
    /**
     * Get all registered drawers
     */
    public static getAllDrawers(): GizmoDrawer[] {
        return Array.from(GizmoManager.drawers.values());
    }
    
    /**
     * Get the singleton instance
     */
    public static getInstance(): GizmoManager | null {
        return GizmoManager.instance;
    }
    
    /**
     * Auto-register all decorated gizmo drawers
     */
    public static autoRegisterDrawers(): void {
        autoRegisterGizmoDrawers((drawer: GizmoDrawer) => {
            GizmoManager.registerDrawer(drawer);
        });
    }

    /**
     * Force refresh all gizmos
     */
    public static refresh(): void {
        const instance = GizmoManager.getInstance();
        if (instance) {
            instance.drawAllGizmos();
        }
    }
}
