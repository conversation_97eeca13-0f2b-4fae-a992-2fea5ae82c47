2025-7-26 23:56:04-debug: start **** info
2025-7-26 23:56:04-log: Cannot access game frame or container.
2025-7-26 23:56:04-debug: asset-db:require-engine-code (420ms)
2025-7-26 23:56:04-log: meshopt wasm decoder initialized
2025-7-26 23:56:04-log: [bullet]:bullet wasm lib loaded.
2025-7-26 23:56:04-log: [box2d]:box2d wasm lib loaded.
2025-7-26 23:56:04-log: Cocos Creator v3.8.6
2025-7-26 23:56:04-log: Using legacy pipeline
2025-7-26 23:56:04-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:31.39MB, end 79.87MB, increase: 48.48MB
2025-7-26 23:56:06-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.08MB, end 290.41MB, increase: 206.33MB
2025-7-26 23:56:05-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.74MB, end 84.05MB, increase: 3.31MB
2025-7-26 23:56:06-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.59MB, end 288.71MB, increase: 208.12MB
2025-7-26 23:56:04-log: Forward render pipeline initialized.
2025-7-26 23:56:06-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.89MB, end 288.74MB, increase: 208.85MB
2025-7-26 23:56:06-debug: run package(taobao-mini-game) handler(enable) start
2025-7-26 23:56:06-debug: run package(taobao-mini-game) handler(enable) success!
2025-7-26 23:56:06-debug: run package(vivo-mini-game) handler(enable) start
2025-7-26 23:56:06-debug: run package(web-desktop) handler(enable) success!
2025-7-26 23:56:06-debug: run package(vivo-mini-game) handler(enable) success!
2025-7-26 23:56:06-debug: run package(web-desktop) handler(enable) start
2025-7-26 23:56:06-debug: run package(web-mobile) handler(enable) success!
2025-7-26 23:56:06-debug: run package(wechatgame) handler(enable) start
2025-7-26 23:56:06-debug: run package(web-mobile) handler(enable) start
2025-7-26 23:56:06-debug: run package(windows) handler(enable) start
2025-7-26 23:56:06-debug: run package(wechatprogram) handler(enable) start
2025-7-26 23:56:06-debug: run package(wechatprogram) handler(enable) success!
2025-7-26 23:56:06-debug: run package(wechatgame) handler(enable) success!
2025-7-26 23:56:06-debug: run package(windows) handler(enable) success!
2025-7-26 23:56:06-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-7-26 23:56:06-debug: run package(cocos-service) handler(enable) start
2025-7-26 23:56:06-debug: run package(xiaomi-quick-game) handler(enable) start
2025-7-26 23:56:06-debug: run package(cocos-service) handler(enable) success!
2025-7-26 23:56:06-debug: run package(im-plugin) handler(enable) success!
2025-7-26 23:56:06-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-7-26 23:56:06-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-7-26 23:56:06-debug: run package(placeholder) handler(enable) success!
2025-7-26 23:56:06-debug: run package(im-plugin) handler(enable) start
2025-7-26 23:56:06-debug: run package(placeholder) handler(enable) start
2025-7-26 23:56:06-debug: asset-db:worker-init: initPlugin (1495ms)
2025-7-26 23:56:06-debug: [Assets Memory track]: asset-db:worker-init start:31.38MB, end 289.90MB, increase: 258.52MB
2025-7-26 23:56:06-debug: Run asset db hook programming:beforePreStart ...
2025-7-26 23:56:06-debug: Run asset db hook engine-extends:beforePreStart ...
2025-7-26 23:56:06-debug: Run asset db hook programming:beforePreStart success!
2025-7-26 23:56:06-debug: Run asset db hook engine-extends:beforePreStart success!
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default-terrain
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\tools
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\physics
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: asset-db:worker-init (2992ms)
2025-7-26 23:56:07-debug: asset-db-hook-programming-beforePreStart (927ms)
2025-7-26 23:56:07-debug: asset-db-hook-engine-extends-beforePreStart (927ms)
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\post-process
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-clip
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-graph-variant
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-graph
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-mask
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\auto-atlas
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\label-atlas
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\effect-header
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\material
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\physics-material
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-texture
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-pipeline
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\terrain
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\typescript
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-freetype
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\2d
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\effects
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies\textures
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\legacy
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\for2d
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\particles
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\color
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\data
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\debug
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\graph-expression
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\math
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\shadow
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\mesh
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\texture
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\data-structures
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\main-functions
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\includes
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\default-functions
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\lighting-flow
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\model-functions
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\data-structures
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\data-structures
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\default-functions
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-graph\ts-animation-graph
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\module-functions
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\effect-header\chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-pipeline\ts-render-flow
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-pipeline\ts-render-pipeline
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-pipeline\ts-render-stage
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\typescript\ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\2d\ui
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies\textures\lut
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\misc
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-planar-shadow
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-shadowmap
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-reflectmap
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\chunks
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-scene\pipeline
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\deprecated.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:07-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\vat
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\common-define.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\decode-standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\decode-base.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\decode.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\fog-base.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\fog-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\input-standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\fog-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\input.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\lighting.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\lightingmap-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\lightingmap-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\local-batch.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\output-standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\morph.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\output.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\sh-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\sh-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shading-cluster-additive.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shading-standard-additive.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shading-standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shading-standard-base.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shadow-map-base.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shading-toon.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shadow-map-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\shadow-map-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\skinning.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\standard-surface-entry.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\post-process\anti-aliasing.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\post-process\fxaa-hq.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\post-process\fxaa.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\post-process\pipeline.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\common-functions.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\fog.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\eye.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\morph-animation.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\probe.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\sh.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\shadow-map.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\skinning-animation-dqs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\skinning-animation-lbs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\functionalities\world-transform.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\alpha-test.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\embedded-alpha.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\particle-common.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\particle-trail.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\particle-vs-legacy.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\particle-vs-gpu.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\sprite-texture.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\internal\sprite-common.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-csm.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-environment.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-diffusemap.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-forward-light.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-global.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-light-map.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-local-batched.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-local.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-reflection-probe.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-morph.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-sh.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-shadow-map.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-shadow.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-skinning.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\builtin\uniforms\cc-world-bound.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\color\aces.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\color\tone-mapping.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\color\gamma.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\data\packing.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\data\unpack.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\debug\debug-view-define.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\effect\fog.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting\attenuation.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\graph-expression\base.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\effect\special-effects.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting\brdf.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting\bxdf.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting\functions.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting\rect-area-light.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\lighting\light-map.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\math\coordinates.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\math\octahedron-transform.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\math\transform.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\mesh\material.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\math\number.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\mesh\vat-animation.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\shadow\native-pcf.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\texture\cubemap.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\texture\texture-lod.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\common\texture\texture-misc.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\main-functions\general-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\main-functions\outline-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\legacy\main-functions\outline-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\data-structures\lighting-intermediate-data.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\data-structures\lighting-misc-data.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\default-functions\simple-skin.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\data-structures\lighting-result.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\default-functions\standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\default-functions\toon.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\default-functions\skin.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\includes\common.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\includes\standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\includes\toon.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\lighting-flow\common-flow.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\includes\unlit.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\lighting-flow\unlit-flow.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\model-functions\standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\model-functions\standard-common.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\data-structures\fs-input.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\lighting-models\model-functions\toon.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\data-structures\vs-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\data-structures\vs-intermediate.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\data-structures\vs-input.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\data-structures\standard.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\data-structures\vs-output.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\data-structures\toon.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\default-functions\common-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\data-structures\unlit.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\default-functions\standard-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\default-functions\skin.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\default-functions\toon-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\common-macros.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\default-functions\unlit-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\render-to-shadowmap.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\render-planar-shadow.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\sky.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\silhouette-edge.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\common-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\unlit.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\effect-macros\terrain.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\common-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\standard-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\standard-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\toon-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\toon-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\module-functions\common-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\includes\unlit-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\module-functions\standard-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\module-functions\debug-view.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\module-functions\toon-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\surfaces\module-functions\unlit-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\misc\silhouette-edge-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\misc\silhouette-edge-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\misc\sky-vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\misc\sky-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-planar-shadow\fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-reflectmap\fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-planar-shadow\vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-scene\fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-scene\vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-shadowmap\fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-shadowmap\vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\chunks\depth.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\chunks\hbao.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\chunks\fsr.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\chunks\vs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\chunks\vs1.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-scene\pipeline\deferred-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\chunks\shading-entries\main-functions\render-to-scene\pipeline\forward-fs.chunk
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\builtin-reflection-probe-preview.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\builtin-standard.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\builtin-terrain.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:08-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\builtin-toon.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:09-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\builtin-unlit.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:09-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\effect\default.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:09-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\effect\effect-surface.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:09-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\car-paint.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:09-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\eye.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:10-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\fabric.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:10-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\glass.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:10-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\hair.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:11-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\leaf.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:11-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\simple-skin.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:11-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\skin.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\sky.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\advanced\water.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\for2d\builtin-spine.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\for2d\builtin-sprite-renderer.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\for2d\builtin-sprite.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-camera-texture.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-clear-stencil.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-debug-renderer.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-geometry-renderer.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-graphics.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-occlusion-query.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\builtin-wireframe.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\legacy\standard.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\legacy\terrain.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:12-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\legacy\toon.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\particles\builtin-billboard.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\particles\builtin-particle-gpu.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\particles\builtin-particle-trail.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\particles\builtin-particle-xr-trail.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\particles\builtin-particle.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\cluster-build.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\cluster-culling.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\copy-pass.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\deferred-lighting.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\float-output-process.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\planar-shadow.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\skybox.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:13-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\smaa.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\ssss-blur.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\tonemap.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\batched-unlit.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\profiler.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\sequence-anim.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\splash-screen.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\box-height-light.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\gizmo.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\grid-2d.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\grid-stroke.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\grid.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\light-probe-visualization.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\light.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\terrain-circle-brush.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\terrain-image-brush.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\internal\editor\terrain-select-brush.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\blit-screen.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\bloom.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\bloom1.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\color-grading.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\color-grading1.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\dof.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\dof1.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\fsr.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\fsr1.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:14-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\fxaa-hq.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\fxaa-hq1.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\hbao.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\post-final.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\taa.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\pipeline\post-process\tone-mapping.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\imported-metallic-roughness.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\imported-specular-glossiness.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\vat\houdini-fluid-v3-liquid.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:15-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\vat\houdini-rigidbody-v2.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\vat\houdini-softbody-v3.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\effects\util\dcc\vat\zeno-fluid-liquid.effect
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-dof-pass.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-pipeline-pass.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-pipeline-settings.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-pipeline-types.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-pipeline.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\tools\debug-view-runtime-control.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:16-debug: Preimport db internal success
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Luban
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\AutoGen
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Luban
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Network
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\AutoGen\Luban
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\AutoGen\PB
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\level
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\GameInstance.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\player
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\IMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Anim.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Background.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\EnemyBullet.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Global.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\GameOver.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Enemy.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Goods.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\MainGame.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Menu.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\PersistNode.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\Player.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\PlayerBullet.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ResUpdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Network\NetMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\AutoGen\Luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\AutoGen\PB\cs_proto.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy\AnimFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy\EnemyBulletFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy\EnemyFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy\GameFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy\GoodsFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\factroy\PlayerBulletFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\Bootstrap.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\WorldInitializeData.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\System.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\SystemContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\World.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\TypeID.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\level\LevelSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\player\PlayerSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:17-debug: Preimport db assets success
2025-7-26 23:56:17-debug: starting packer-driver...
2025-7-26 23:56:17-debug: Run asset db hook programming:afterPreStart ...
2025-7-26 23:56:21-debug: initialize scripting environment...
2025-7-26 23:56:21-debug: [[Executor]] prepare before lock
2025-7-26 23:56:21-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-7-26 23:56:21-debug: [[Executor]] prepare after unlock
2025-7-26 23:56:21-debug: Run asset db hook programming:afterPreStart success!
2025-7-26 23:56:21-debug: Run asset db hook engine-extends:afterPreStart ...
2025-7-26 23:56:21-debug: asset-db-hook-programming-afterPreStart (4486ms)
2025-7-26 23:56:21-debug: recompile effect.bin success
2025-7-26 23:56:21-debug: Run asset db hook engine-extends:afterPreStart success!
2025-7-26 23:56:21-debug: [Assets Memory track]: asset-db:worker-init: preStart start:289.92MB, end 408.09MB, increase: 118.17MB
2025-7-26 23:56:21-debug: Start up the 'internal' database...
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default-video.mp4
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\Default-Particle.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default-terrain\default-layer-texture.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\back.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: asset-db:worker-effect-data-processing (733ms)
2025-7-26 23:56:22-debug: asset-db-hook-engine-extends-afterPreStart (733ms)
2025-7-26 23:56:22-debug: Status file D:\Workspace\Projects\Moolego\M2Game\Client\temp\asset-db\internal\fbx.FBX-glTF-conv\1263d74c-8167-4928-91a6-4e2672411f47\status.json: Error: ENOENT: no such file or directory, open 'D:\Workspace\Projects\Moolego\M2Game\Client\temp\asset-db\internal\fbx.FBX-glTF-conv\1263d74c-8167-4928-91a6-4e2672411f47\status.json'
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\Default-Particle.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\back.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default-terrain\default-layer-texture.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\bottom.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\front.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\left.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\right.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\bottom.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\front.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\left.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\top.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-billboard-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-clear-stencil.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\right.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-material-transparent.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-particle-gpu-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-particle-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-spine-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_cubemap\top.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-sprite-renderer-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\default-trail-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\missing-effect-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\missing-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\particle-add.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\standard-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-alpha-test-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-base-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-graphics-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-sprite-alpha-sep-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-sprite-gray-alpha-sep-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-sprite-gray-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_materials\ui-sprite-material.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\Camera.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\Terrain.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-bloom.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-color-grading.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-deferred.rpp
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-depth-of-field.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-fsr.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-forward.rpp
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-fxaa.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\builtin-tone-mapping.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\deferred-lighting.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\post-process.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_renderpipeline\tonemap.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: Start to conver asset {asset[d032ac98-05e1-4090-88bb-eb640dcb5fc1](d032ac98-05e1-4090-88bb-eb640dcb5fc1)}
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\atom.plist
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\atom.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\atom.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\atom.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\atom_new.plist
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_disabled.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: Conver assetC:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr -> PNG success.
2025-7-26 23:56:22-debug: Frame rate: 24 [{asset(1263d74c-8167-4928-91a6-4e2672411f47)}]
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_disabled.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_normal.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@17020
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_disabled.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@801ec
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_pressed.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_normal.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_normal.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@2e76e
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_pressed.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_editbox_bg.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0@74afd
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_btn_pressed.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@38fd2
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0@8fd34
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_panel.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@40ece
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_editbox_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0@bb97f
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@fc873
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_editbox_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_panel.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0@7d38f
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@8abdc
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_panel.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_progressbar.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0@e9a6d
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_progressbar_bg.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@a804a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.png@b47c0@40c10
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@8d883
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_progressbar.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\primitives.fbx@aae0f
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_progressbar.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_radio_button_off.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_progressbar_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_radio_button_on.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_progressbar_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_bg.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0@74afd
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_radio_button_off.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_radio_button_on.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_radio_button_on.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_radio_button_off.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0@8fd34
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_vertical.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_vertical_bg.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0@bb97f
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_sprite.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0@7d38f
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_sprite_splash.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_vertical_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_vertical.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0@e9a6d
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_vertical_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_scrollbar_vertical.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_sprite.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_sprite.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_skybox\default_skybox.hdr@b47c0@40c10
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_checkmark.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_sprite_splash.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_disabled.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_sprite_splash.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_normal.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_pressed.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\camera.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_checkmark.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_disabled.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_checkmark.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_normal.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\directional-light.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_pressed.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_normal.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\default_toggle_pressed.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\light-probe.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\particle-system.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\camera.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\reflection-probe.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\directional-light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\scene-gizmo.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\sphere-light.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\spot-light.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\light-probe.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\webview.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\particle-system.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\reflection-probe.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\sphere-light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\x.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\y.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\z.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\spot-light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\webview.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\physics\default-physics-material.pmtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\tools\debug-view-runtime-control.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\tools\parsed-effect-info.json
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\x.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\y.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\z.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\x.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\y.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:22-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\gizmo\z.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-clip\default.anim
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-graph\default.animgraph
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-graph-variant\default.animgraphvari
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\animation-mask\default.animask
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\auto-atlas\default.pac
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\label-atlas\default.labelatlas
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\material\default.mtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\physics-material\default.pmtl
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\prefab\default.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-pipeline\default.rpp
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-pipeline\forward-pipeline.rpp
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-texture\default.rt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\scene\default.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\scene\scene-2d.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\scene\scene-quality.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\terrain\default.terrain
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_file_content\render-texture\default.rt@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Bold.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-BoldItalic.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-BoldItalic_0.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Bold_0.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Italic.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Italic_0.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Regular.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Bold_0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-BoldItalic_0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Bold_0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-BoldItalic_0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Italic_0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Regular_0.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Italic_0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-freetype\OpenSans-Bold.ttf
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-freetype\OpenSans-BoldItalic.ttf
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-freetype\OpenSans-Italic.ttf
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-freetype\OpenSans-Regular.ttf
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Regular_0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Regular_0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\2d\Camera.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Capsule.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Cone.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Cube.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Cylinder.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Plane.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Quad.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Sphere.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\3d\Torus.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\effects\Particle System.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Directional Light.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Light Probe Group.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Point Light.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Ranged Directional Light.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Reflection Probe.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Sphere Light.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\light\Spot Light.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Button.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Canvas.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\EditBox.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Graphics.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Label.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Layout.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Mask.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\pageView.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\ParticleSystem2D.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\ProgressBar.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\RichText.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\ScrollView.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Slider.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Sprite.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\SpriteRenderer.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\SpriteSplash.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\TiledMap.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Toggle.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\ToggleContainer.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\VideoPlayer.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\WebView.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\ui\Widget.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies\textures\preintegrated-skin.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_prefab\2d\ui\Canvas.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies\textures\lut\original-color-grading.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies\textures\preintegrated-skin.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\dependencies\textures\lut\original-color-grading.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-BoldItalic.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Bold.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Italic.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_fonts\builtin-bitmap\OpenSans-Regular.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\editor\assets\default_ui\atom.plist
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:290.01MB, end 429.11MB, increase: 139.11MB
2025-7-26 23:56:23-debug: Start up the 'assets' database...
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\Main.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\ResUpdate.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\Anim.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: asset-db:worker-startup-database[internal] (16709ms)
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\boom.anim
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\Enemy.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\EnemyBullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\Goods.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\mikado_outline_shadow.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\mikado_outline_shadow.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\PlayerBullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Luban\tbglobalattr.json
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Luban\tbweapon.json
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\jkyxgg.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\loadingBG.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\loadingPB1.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio\boom.mp3
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\jkyxgg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\jkyxgg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\loadingPB1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\loadingBG.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\loadingPB1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ResUpdate\loadingBG.jpg@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio\bullet.mp3
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\mikado_outline_shadow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio\gameover.mp3
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\mikado_outline_shadow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio\game_music.wav
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio\lightgun.mp3
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\audio\missile.mp3
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\background_game.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\background_menu.jpg
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\button_menu.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\logo.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b0.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\background_game.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\background_game.jpg@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\background_menu.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\background_menu.jpg@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\logo.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b1.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b2.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\logo.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\button_menu.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\background\button_menu.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b3.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\backup.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\enemybullet1.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\enemybullet2.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\light.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\boom\b3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\backup.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\enemybullet1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\backup.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\missile.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\enemybullet1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\normal.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\enemybullet2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\enemybullet2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy\enemy1.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy\enemy2.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\missile.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\light.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\missile.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\blood.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\normal.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\bullets\normal.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\light.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy\enemy1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy\enemy2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy\enemy1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\enemy\enemy2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\missile.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_1.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_2.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\blood.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\light.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\blood.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_3.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\missile.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\goods\missile.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\images\player\plane_3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\mikado_outline_shadow.fnt
background: #aaff85; color: #000;
color: #000;
2025-7-26 23:56:23-debug: lazy register asset handler *
2025-7-26 23:56:23-debug: lazy register asset handler directory
2025-7-26 23:56:23-debug: lazy register asset handler spine-data
2025-7-26 23:56:23-debug: lazy register asset handler text
2025-7-26 23:56:23-debug: lazy register asset handler dragonbones
2025-7-26 23:56:23-debug: lazy register asset handler dragonbones-atlas
2025-7-26 23:56:23-debug: lazy register asset handler javascript
2025-7-26 23:56:23-debug: lazy register asset handler terrain
2025-7-26 23:56:23-debug: lazy register asset handler typescript
2025-7-26 23:56:23-debug: lazy register asset handler json
2025-7-26 23:56:23-debug: lazy register asset handler scene
2025-7-26 23:56:23-debug: lazy register asset handler tiled-map
2025-7-26 23:56:23-debug: lazy register asset handler buffer
2025-7-26 23:56:23-debug: lazy register asset handler image
2025-7-26 23:56:23-debug: lazy register asset handler sprite-frame
2025-7-26 23:56:23-debug: lazy register asset handler sign-image
2025-7-26 23:56:23-debug: lazy register asset handler prefab
2025-7-26 23:56:23-debug: lazy register asset handler erp-texture-cube
2025-7-26 23:56:23-debug: lazy register asset handler render-texture
2025-7-26 23:56:23-debug: lazy register asset handler texture-cube
2025-7-26 23:56:23-debug: lazy register asset handler texture-cube-face
2025-7-26 23:56:23-debug: lazy register asset handler texture
2025-7-26 23:56:23-debug: lazy register asset handler rt-sprite-frame
2025-7-26 23:56:23-debug: lazy register asset handler gltf
2025-7-26 23:56:23-debug: lazy register asset handler alpha-image
2025-7-26 23:56:23-debug: lazy register asset handler gltf-animation
2025-7-26 23:56:23-debug: lazy register asset handler gltf-material
2025-7-26 23:56:23-debug: lazy register asset handler gltf-scene
2025-7-26 23:56:23-debug: lazy register asset handler gltf-skeleton
2025-7-26 23:56:23-debug: lazy register asset handler gltf-embeded-image
2025-7-26 23:56:23-debug: lazy register asset handler gltf-mesh
2025-7-26 23:56:23-debug: lazy register asset handler fbx
2025-7-26 23:56:23-debug: lazy register asset handler material
2025-7-26 23:56:23-debug: lazy register asset handler physics-material
2025-7-26 23:56:23-debug: lazy register asset handler effect
2025-7-26 23:56:23-debug: lazy register asset handler audio-clip
2025-7-26 23:56:23-debug: lazy register asset handler animation-graph
2025-7-26 23:56:23-debug: lazy register asset handler animation-graph-variant
2025-7-26 23:56:23-debug: lazy register asset handler animation-mask
2025-7-26 23:56:23-debug: lazy register asset handler animation-clip
2025-7-26 23:56:23-debug: lazy register asset handler bitmap-font
2025-7-26 23:56:23-debug: lazy register asset handler ttf-font
2025-7-26 23:56:23-debug: lazy register asset handler particle
2025-7-26 23:56:23-debug: lazy register asset handler effect-header
2025-7-26 23:56:23-debug: lazy register asset handler auto-atlas
2025-7-26 23:56:23-debug: lazy register asset handler sprite-atlas
2025-7-26 23:56:23-debug: lazy register asset handler render-pipeline
2025-7-26 23:56:23-debug: lazy register asset handler render-stage
2025-7-26 23:56:23-debug: lazy register asset handler render-flow
2025-7-26 23:56:23-debug: lazy register asset handler label-atlas
2025-7-26 23:56:23-debug: lazy register asset handler instantiation-mesh
2025-7-26 23:56:23-debug: lazy register asset handler instantiation-skeleton
2025-7-26 23:56:23-debug: lazy register asset handler instantiation-animation
2025-7-26 23:56:23-debug: lazy register asset handler instantiation-material
2025-7-26 23:56:23-debug: lazy register asset handler video-clip
2025-7-26 23:56:23-debug: asset-db:worker-startup-database[assets] (6441ms)
2025-7-26 23:56:23-debug: asset-db:start-database (16897ms)
2025-7-26 23:56:23-debug: asset-db:ready (21316ms)
2025-7-26 23:56:23-debug: fix the bug of updateDefaultUserData
2025-7-26 23:56:23-debug: init worker message success
2025-7-26 23:56:23-debug: programming:execute-script (-11ms)
2025-7-26 23:56:23-debug: [Build Memory track]: builder:worker-init start:198.77MB, end 206.75MB, increase: 7.99MB
2025-7-26 23:56:23-debug: builder:worker-init (259ms)
2025-7-27 00:28:24-debug: refresh db internal success
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\EmitterArc.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\Object.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:28:24-debug: refresh db assets success
2025-7-27 00:28:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:28:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:28:24-debug: asset-db:refresh-all-database (66ms)
2025-7-27 00:28:24-debug: asset-db:worker-effect-data-processing (2ms)
2025-7-27 00:28:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-27 00:40:52-debug: refresh db internal success
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\gizmos\EmitterGizmo.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoSetup.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:40:52-debug: refresh db assets success
2025-7-27 00:40:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:40:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:40:52-debug: asset-db:refresh-all-database (83ms)
2025-7-27 00:41:12-debug: refresh db internal success
2025-7-27 00:41:12-debug: refresh db assets success
2025-7-27 00:41:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:41:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:41:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:41:12-debug: asset-db:refresh-all-database (49ms)
2025-7-27 00:41:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:41:42-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:41:42-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (8ms)
2025-7-27 00:42:02-debug: refresh db internal success
2025-7-27 00:42:02-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:42:02-debug: refresh db assets success
2025-7-27 00:42:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:42:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:42:02-debug: asset-db:refresh-all-database (41ms)
2025-7-27 00:42:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:42:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:42:09-debug: refresh db internal success
2025-7-27 00:42:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:42:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:42:09-debug: refresh db assets success
2025-7-27 00:42:09-debug: asset-db:refresh-all-database (40ms)
2025-7-27 00:42:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:42:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:42:13-debug: refresh db internal success
2025-7-27 00:42:13-debug: refresh db assets success
2025-7-27 00:42:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:42:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:42:13-debug: asset-db:refresh-all-database (38ms)
2025-7-27 00:42:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:42:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:42:14-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:42:14-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (11ms)
2025-7-27 00:45:38-debug: refresh db internal success
2025-7-27 00:45:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\System.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:45:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\SystemContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:45:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\World.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:45:38-debug: refresh db assets success
2025-7-27 00:45:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:45:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:45:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:45:38-debug: asset-db:refresh-all-database (49ms)
2025-7-27 00:45:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:45:44-debug: Query all assets info in project
2025-7-27 00:45:44-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:45:44-debug: Skip compress image, progress: 0%
2025-7-27 00:45:44-debug: Init all bundles start..., progress: 0%
2025-7-27 00:45:44-debug: Query asset bundle start, progress: 0%
2025-7-27 00:45:44-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:44-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:45:44-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:45:44-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:45:44-debug:   Number of all scenes: 4
2025-7-27 00:45:44-debug:   Number of other assets: 402
2025-7-27 00:45:44-debug:   Number of all scripts: 52
2025-7-27 00:45:44-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:45:44-log: run build task Query asset bundle success in 5 ms√, progress: 5%
2025-7-27 00:45:44-debug: [Build Memory track]: Query asset bundle start:209.69MB, end 210.20MB, increase: 529.72KB
2025-7-27 00:45:44-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:44-debug: // ---- build task Query asset bundle ---- (5ms)
2025-7-27 00:45:44-debug: Query asset bundle start, progress: 5%
2025-7-27 00:45:44-debug: // ---- build task Query asset bundle ---- (1ms)
2025-7-27 00:45:44-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-7-27 00:45:44-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:45:44-debug: [Build Memory track]: Query asset bundle start:210.23MB, end 209.63MB, increase: -618.63KB
2025-7-27 00:45:44-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:44-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-27 00:45:44-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-27 00:45:44-debug: [Build Memory track]: Sort some build options to settings.json start:209.66MB, end 209.68MB, increase: 26.39KB
2025-7-27 00:45:44-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:45:44-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:45:44-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-27 00:45:44-debug: [Build Memory track]: Fill script data to settings start:209.71MB, end 209.73MB, increase: 17.55KB
2025-7-27 00:45:44-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:44-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:45:44-debug: // ---- build task Sort some build options to settings.json ---- (6ms)
2025-7-27 00:45:44-log: run build task Sort some build options to settings.json success in 6 ms√, progress: 15%
2025-7-27 00:45:44-debug: [Build Memory track]: Sort some build options to settings.json start:209.75MB, end 209.89MB, increase: 140.56KB
2025-7-27 00:45:44-debug: Query all assets info in project
2025-7-27 00:45:44-debug: Query all assets info in project
2025-7-27 00:45:44-debug: Query all assets info in project
2025-7-27 00:45:44-debug: Query all assets info in project
2025-7-27 00:45:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:45:45-debug: Skip compress image, progress: 0%
2025-7-27 00:45:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:45:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:45:45-debug: Skip compress image, progress: 0%
2025-7-27 00:45:45-debug: Skip compress image, progress: 0%
2025-7-27 00:45:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:45:45-debug: Skip compress image, progress: 0%
2025-7-27 00:45:45-debug: Init all bundles start..., progress: 0%
2025-7-27 00:45:45-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 0%
2025-7-27 00:45:45-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 0%
2025-7-27 00:45:45-debug: Init all bundles start..., progress: 0%
2025-7-27 00:45:45-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:45:45-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:45:45-debug: Init all bundles start..., progress: 0%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:45:45-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:45:45-debug: Init all bundles start..., progress: 0%
2025-7-27 00:45:45-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 0%
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 0%
2025-7-27 00:45:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:45:45-debug:   Number of all scenes: 4
2025-7-27 00:45:45-debug:   Number of other assets: 402
2025-7-27 00:45:45-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:45:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:45:45-debug:   Number of all scenes: 4
2025-7-27 00:45:45-debug:   Number of all scripts: 52
2025-7-27 00:45:45-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:45:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:45:45-debug:   Number of other assets: 402
2025-7-27 00:45:45-debug:   Number of all scripts: 52
2025-7-27 00:45:45-debug:   Number of all scenes: 4
2025-7-27 00:45:45-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:45:45-debug:   Number of all scripts: 52
2025-7-27 00:45:45-debug:   Number of other assets: 402
2025-7-27 00:45:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:45:45-debug:   Number of all scripts: 52
2025-7-27 00:45:45-debug:   Number of other assets: 402
2025-7-27 00:45:45-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:45:45-debug:   Number of all scenes: 4
2025-7-27 00:45:45-log: run build task Query asset bundle success in 8 ms√, progress: 5%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ---- (8ms)
2025-7-27 00:45:45-debug: [Build Memory track]: Query asset bundle start:210.19MB, end 210.89MB, increase: 717.56KB
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 5%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-log: run build task Query asset bundle success in √, progress: 5%
2025-7-27 00:45:45-debug: [Build Memory track]: Query asset bundle start:210.92MB, end 210.94MB, increase: 18.54KB
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 5%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-debug: [Build Memory track]: Query asset bundle start:210.96MB, end 210.98MB, increase: 12.77KB
2025-7-27 00:45:45-log: run build task Query asset bundle success in √, progress: 5%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-log: run build task Query asset bundle success in √, progress: 5%
2025-7-27 00:45:45-debug: [Build Memory track]: Query asset bundle start:211.02MB, end 211.03MB, increase: 13.02KB
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 5%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:45-debug: Query asset bundle start, progress: 5%
2025-7-27 00:45:45-debug: // ---- build task Query asset bundle ---- (1ms)
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:45:45-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-7-27 00:45:45-debug: [Build Memory track]: Query asset bundle start:211.06MB, end 210.59MB, increase: -472.77KB
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-log: run build task Query asset bundle success in √, progress: 10%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-log: run build task Query asset bundle success in √, progress: 10%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-log: run build task Query asset bundle success in √, progress: 10%
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-27 00:45:45-debug: [Build Memory track]: Sort some build options to settings.json start:210.73MB, end 210.76MB, increase: 31.46KB
2025-7-27 00:45:45-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:45:45-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-27 00:45:45-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-27 00:45:45-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:45:45-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:45:45-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:45:45-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-7-27 00:45:45-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:45:45-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-27 00:45:45-debug: [Build Memory track]: Fill script data to settings start:210.92MB, end 210.94MB, increase: 20.18KB
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-7-27 00:45:45-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-27 00:45:45-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:45-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:45:45-debug: // ---- build task Sort some build options to settings.json ---- (3ms)
2025-7-27 00:45:45-debug: [Build Memory track]: Sort some build options to settings.json start:211.07MB, end 210.77MB, increase: -308.13KB
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in 3 ms√, progress: 15%
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-7-27 00:45:45-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-7-27 00:45:50-debug: Query all assets info in project
2025-7-27 00:45:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:45:50-debug: Skip compress image, progress: 0%
2025-7-27 00:45:50-debug: Init all bundles start..., progress: 0%
2025-7-27 00:45:50-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:45:50-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:45:50-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:50-debug: Query asset bundle start, progress: 0%
2025-7-27 00:45:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:45:50-debug:   Number of all scenes: 4
2025-7-27 00:45:50-debug:   Number of all scripts: 52
2025-7-27 00:45:50-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:45:50-debug: // ---- build task Query asset bundle ---- (3ms)
2025-7-27 00:45:50-debug: [Build Memory track]: Query asset bundle start:211.11MB, end 210.65MB, increase: -474.40KB
2025-7-27 00:45:50-debug:   Number of other assets: 402
2025-7-27 00:45:50-debug: Query asset bundle start, progress: 5%
2025-7-27 00:45:50-log: run build task Query asset bundle success in 3 ms√, progress: 5%
2025-7-27 00:45:50-debug: // ---- build task Query asset bundle ----
2025-7-27 00:45:50-debug: // ---- build task Query asset bundle ---- (1ms)
2025-7-27 00:45:50-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-7-27 00:45:50-debug: [Build Memory track]: Query asset bundle start:210.68MB, end 210.78MB, increase: 101.31KB
2025-7-27 00:45:50-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:45:50-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:50-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-7-27 00:45:50-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:45:50-debug: [Build Memory track]: Sort some build options to settings.json start:210.81MB, end 210.82MB, increase: 18.20KB
2025-7-27 00:45:50-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:45:50-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-27 00:45:50-debug: [Build Memory track]: Fill script data to settings start:210.85MB, end 210.87MB, increase: 16.32KB
2025-7-27 00:45:50-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:45:50-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:45:50-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-7-27 00:45:50-debug: [Build Memory track]: Sort some build options to settings.json start:210.90MB, end 211.02MB, increase: 131.86KB
2025-7-27 00:45:50-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-7-27 00:47:09-debug: refresh db internal success
2025-7-27 00:47:10-debug: refresh db assets success
2025-7-27 00:47:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:47:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:47:10-debug: asset-db:refresh-all-database (47ms)
2025-7-27 00:50:38-debug: refresh db internal success
2025-7-27 00:50:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:50:38-debug: refresh db assets success
2025-7-27 00:50:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:50:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:50:38-debug: asset-db:refresh-all-database (48ms)
2025-7-27 00:50:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:50:42-debug: Query all assets info in project
2025-7-27 00:50:42-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-27 00:50:42-debug: Skip compress image, progress: 0%
2025-7-27 00:50:42-debug: Init all bundles start..., progress: 0%
2025-7-27 00:50:42-debug: // ---- build task Query asset bundle ----
2025-7-27 00:50:42-debug: Init bundle root assets start..., progress: 0%
2025-7-27 00:50:42-debug: Query asset bundle start, progress: 0%
2025-7-27 00:50:42-debug: Num of bundles: 3..., progress: 0%
2025-7-27 00:50:42-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-27 00:50:42-debug:   Number of all scenes: 4
2025-7-27 00:50:42-debug:   Number of other assets: 402
2025-7-27 00:50:42-debug: Init bundle root assets success..., progress: 0%
2025-7-27 00:50:42-debug:   Number of all scripts: 52
2025-7-27 00:50:42-log: run build task Query asset bundle success in 19 ms√, progress: 5%
2025-7-27 00:50:42-debug: // ---- build task Query asset bundle ---- (19ms)
2025-7-27 00:50:42-debug: [Build Memory track]: Query asset bundle start:213.37MB, end 213.72MB, increase: 355.23KB
2025-7-27 00:50:42-debug: Query asset bundle start, progress: 5%
2025-7-27 00:50:42-debug: // ---- build task Query asset bundle ----
2025-7-27 00:50:42-debug: // ---- build task Query asset bundle ---- (2ms)
2025-7-27 00:50:42-debug: [Build Memory track]: Query asset bundle start:213.75MB, end 213.15MB, increase: -611.76KB
2025-7-27 00:50:42-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-7-27 00:50:42-debug: Sort some build options to settings.json start, progress: 10%
2025-7-27 00:50:42-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:50:42-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-27 00:50:42-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-27 00:50:42-debug: [Build Memory track]: Sort some build options to settings.json start:213.18MB, end 213.21MB, increase: 25.97KB
2025-7-27 00:50:42-debug: // ---- build task Fill script data to settings ----
2025-7-27 00:50:42-debug: Fill script data to settings start, progress: 12%
2025-7-27 00:50:42-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-7-27 00:50:42-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-7-27 00:50:42-debug: [Build Memory track]: Fill script data to settings start:213.23MB, end 213.26MB, increase: 26.17KB
2025-7-27 00:50:42-debug: Sort some build options to settings.json start, progress: 13%
2025-7-27 00:50:42-debug: // ---- build task Sort some build options to settings.json ----
2025-7-27 00:50:42-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-7-27 00:50:42-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-7-27 00:50:42-debug: [Build Memory track]: Sort some build options to settings.json start:213.29MB, end 213.41MB, increase: 129.20KB
2025-7-27 00:52:40-debug: refresh db internal success
2025-7-27 00:52:40-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:52:40-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:52:40-debug: refresh db assets success
2025-7-27 00:52:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:52:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:52:40-debug: asset-db:refresh-all-database (50ms)
2025-7-27 00:52:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:52:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:52:51-debug: refresh db internal success
2025-7-27 00:52:51-debug: refresh db assets success
2025-7-27 00:52:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:52:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:52:51-debug: asset-db:refresh-all-database (38ms)
2025-7-27 00:52:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 00:52:55-debug: refresh db internal success
2025-7-27 00:52:55-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:52:55-debug: refresh db assets success
2025-7-27 00:52:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:52:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:52:55-debug: asset-db:refresh-all-database (37ms)
2025-7-27 00:53:26-debug: refresh db internal success
2025-7-27 00:53:26-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 00:53:26-debug: refresh db assets success
2025-7-27 00:53:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 00:53:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 00:53:26-debug: asset-db:refresh-all-database (48ms)
2025-7-27 00:53:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 00:53:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-27 22:08:27-debug: refresh db internal success
2025-7-27 22:08:27-debug: refresh db assets success
2025-7-27 22:08:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:08:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:08:27-debug: asset-db:refresh-all-database (46ms)
2025-7-27 22:08:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:08:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:12:03-debug: refresh db internal success
2025-7-27 22:12:03-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:12:03-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:12:03-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:12:03-debug: refresh db assets success
2025-7-27 22:12:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:03-debug: asset-db:refresh-all-database (61ms)
2025-7-27 22:12:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:12:07-debug: refresh db internal success
2025-7-27 22:12:07-debug: refresh db assets success
2025-7-27 22:12:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:07-debug: asset-db:refresh-all-database (65ms)
2025-7-27 22:12:12-debug: refresh db internal success
2025-7-27 22:12:12-debug: refresh db assets success
2025-7-27 22:12:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:12-debug: asset-db:refresh-all-database (41ms)
2025-7-27 22:12:45-debug: refresh db internal success
2025-7-27 22:12:45-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:12:45-debug: refresh db assets success
2025-7-27 22:12:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:45-debug: asset-db:refresh-all-database (49ms)
2025-7-27 22:12:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:12:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:12:50-debug: refresh db internal success
2025-7-27 22:12:50-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:12:50-debug: refresh db assets success
2025-7-27 22:12:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:50-debug: asset-db:refresh-all-database (42ms)
2025-7-27 22:12:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:12:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:12:54-debug: refresh db internal success
2025-7-27 22:12:54-debug: refresh db assets success
2025-7-27 22:12:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:54-debug: asset-db:refresh-all-database (37ms)
2025-7-27 22:12:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:12:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:12:55-debug: refresh db internal success
2025-7-27 22:12:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:12:55-debug: refresh db assets success
2025-7-27 22:12:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:12:55-debug: asset-db:refresh-all-database (36ms)
2025-7-27 22:12:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:13:00-debug: refresh db internal success
2025-7-27 22:13:00-debug: refresh db assets success
2025-7-27 22:13:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:13:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:13:00-debug: asset-db:refresh-all-database (35ms)
2025-7-27 22:13:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:13:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:13:03-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:13:03-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (10ms)
2025-7-27 22:26:38-debug: refresh db internal success
2025-7-27 22:26:38-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoSetup.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:38-debug: refresh db assets success
2025-7-27 22:26:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:26:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:26:38-debug: asset-db:refresh-all-database (50ms)
2025-7-27 22:26:41-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:26:41-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (9ms)
2025-7-27 22:26:41-debug: refresh db internal success
2025-7-27 22:26:41-debug: refresh db assets success
2025-7-27 22:26:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:26:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:26:41-debug: asset-db:refresh-all-database (48ms)
2025-7-27 22:26:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:26:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:27:18-debug: refresh db internal success
2025-7-27 22:27:18-debug: refresh db assets success
2025-7-27 22:27:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:27:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:27:18-debug: asset-db:refresh-all-database (39ms)
2025-7-27 22:27:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:27:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:41:48-debug: refresh db internal success
2025-7-27 22:41:48-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:41:48-debug: refresh db assets success
2025-7-27 22:41:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:41:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:41:48-debug: asset-db:refresh-all-database (51ms)
2025-7-27 22:41:48-debug: asset-db:worker-effect-data-processing (4ms)
2025-7-27 22:41:48-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-7-27 22:42:38-debug: refresh db internal success
2025-7-27 22:42:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\base\TypeID.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:42:38-debug: refresh db assets success
2025-7-27 22:42:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:42:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:42:38-debug: asset-db:refresh-all-database (45ms)
2025-7-27 22:42:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:42:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:43:14-debug: refresh db internal success
2025-7-27 22:43:14-debug: refresh db assets success
2025-7-27 22:43:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:43:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:43:14-debug: asset-db:refresh-all-database (43ms)
2025-7-27 22:43:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:43:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:43:19-debug: refresh db internal success
2025-7-27 22:43:19-debug: refresh db assets success
2025-7-27 22:43:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:43:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:43:19-debug: asset-db:refresh-all-database (41ms)
2025-7-27 22:43:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:43:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 22:43:34-debug: refresh db internal success
2025-7-27 22:43:34-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-27 22:43:34-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:43:34-debug: refresh db assets success
2025-7-27 22:43:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:43:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:43:34-debug: asset-db:refresh-all-database (144ms)
2025-7-27 22:44:02-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 22:44:02-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (10ms)
2025-7-27 22:44:02-debug: refresh db internal success
2025-7-27 22:44:02-debug: refresh db assets success
2025-7-27 22:44:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 22:44:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 22:44:02-debug: asset-db:refresh-all-database (36ms)
2025-7-27 22:44:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 22:44:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:00:19-debug: refresh db internal success
2025-7-27 23:00:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:00:19-debug: refresh db assets success
2025-7-27 23:00:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:00:19-debug: asset-db:refresh-all-database (40ms)
2025-7-27 23:00:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:00:19-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-27 23:03:23-debug: refresh db internal success
2025-7-27 23:03:23-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:03:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:03:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:03:23-debug: refresh db assets success
2025-7-27 23:03:23-debug: asset-db:refresh-all-database (47ms)
2025-7-27 23:03:50-debug: refresh db internal success
2025-7-27 23:03:50-debug: refresh db assets success
2025-7-27 23:03:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:03:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:03:50-debug: asset-db:refresh-all-database (38ms)
2025-7-27 23:03:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:03:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:03:55-debug: refresh db internal success
2025-7-27 23:03:55-debug: refresh db assets success
2025-7-27 23:03:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:03:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:03:55-debug: asset-db:refresh-all-database (36ms)
2025-7-27 23:03:57-debug: refresh db internal success
2025-7-27 23:03:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:03:57-debug: refresh db assets success
2025-7-27 23:03:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:03:57-debug: asset-db:refresh-all-database (38ms)
2025-7-27 23:04:06-debug: refresh db internal success
2025-7-27 23:04:06-debug: refresh db assets success
2025-7-27 23:04:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:04:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:04:06-debug: asset-db:refresh-all-database (43ms)
2025-7-27 23:04:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:04:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:04:38-debug: refresh db internal success
2025-7-27 23:04:38-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:04:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:04:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:04:38-debug: refresh db assets success
2025-7-27 23:04:38-debug: asset-db:refresh-all-database (49ms)
2025-7-27 23:04:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:04:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:04:44-debug: refresh db internal success
2025-7-27 23:04:44-debug: refresh db assets success
2025-7-27 23:04:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:04:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:04:44-debug: asset-db:refresh-all-database (57ms)
2025-7-27 23:04:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-7-27 23:04:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-27 23:04:45-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:04:45-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (14ms)
2025-7-27 23:05:52-debug: refresh db internal success
2025-7-27 23:05:52-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:05:52-debug: refresh db assets success
2025-7-27 23:05:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:05:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:05:52-debug: asset-db:refresh-all-database (49ms)
2025-7-27 23:06:40-debug: refresh db internal success
2025-7-27 23:06:40-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:06:40-debug: refresh db assets success
2025-7-27 23:06:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:06:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:06:40-debug: asset-db:refresh-all-database (45ms)
2025-7-27 23:06:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:06:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:07:22-debug: refresh db internal success
2025-7-27 23:07:22-debug: refresh db assets success
2025-7-27 23:07:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:07:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:07:22-debug: asset-db:refresh-all-database (43ms)
2025-7-27 23:07:49-debug: refresh db internal success
2025-7-27 23:07:49-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:07:49-debug: refresh db assets success
2025-7-27 23:07:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:07:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:07:49-debug: asset-db:refresh-all-database (47ms)
2025-7-27 23:08:27-debug: refresh db internal success
2025-7-27 23:08:27-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:08:27-debug: refresh db assets success
2025-7-27 23:08:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:08:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:08:27-debug: asset-db:refresh-all-database (50ms)
2025-7-27 23:08:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:08:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:09:09-debug: refresh db internal success
2025-7-27 23:09:10-debug: refresh db assets success
2025-7-27 23:09:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:09:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:09:10-debug: asset-db:refresh-all-database (43ms)
2025-7-27 23:09:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:09:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:09:40-debug: refresh db internal success
2025-7-27 23:09:40-debug: refresh db assets success
2025-7-27 23:09:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:09:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:09:40-debug: asset-db:refresh-all-database (48ms)
2025-7-27 23:09:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:09:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:13:13-debug: refresh db internal success
2025-7-27 23:13:13-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:13:13-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:13:13-debug: refresh db assets success
2025-7-27 23:13:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:13:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:13:13-debug: asset-db:refresh-all-database (51ms)
2025-7-27 23:13:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:17:30-debug: refresh db internal success
2025-7-27 23:17:30-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:17:30-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:17:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:17:30-debug: refresh db assets success
2025-7-27 23:17:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:17:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:17:30-debug: asset-db:refresh-all-database (52ms)
2025-7-27 23:17:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:17:35-debug: refresh db internal success
2025-7-27 23:17:35-debug: refresh db assets success
2025-7-27 23:17:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:17:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:17:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:17:35-debug: asset-db:refresh-all-database (40ms)
2025-7-27 23:17:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:17:37-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:17:37-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (10ms)
2025-7-27 23:17:37-debug: refresh db internal success
2025-7-27 23:17:37-debug: refresh db assets success
2025-7-27 23:17:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:17:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:17:37-debug: asset-db:refresh-all-database (39ms)
2025-7-27 23:17:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-27 23:17:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-27 23:19:59-debug: refresh db internal success
2025-7-27 23:19:59-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:19:59-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-27 23:19:59-debug: refresh db assets success
2025-7-27 23:19:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-27 23:19:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-27 23:19:59-debug: asset-db:refresh-all-database (47ms)
2025-7-27 23:19:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
