{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"], "names": ["_decorator", "Component", "Graphics", "EDITOR", "autoRegisterGizmoDrawers", "ccclass", "property", "executeInEditMode", "GizmoManager", "graphics", "updateTimer", "updateInterval", "onLoad", "drawInPlayMode", "instance", "getComponent", "addComponent", "refreshRate", "autoRegisterDrawers", "console", "log", "onDestroy", "update", "deltaTime", "gizmosEnabled", "drawAllGizmos", "clear", "drawers", "size", "sortedDrawers", "Array", "from", "values", "filter", "drawer", "enabled", "sort", "a", "b", "getPriority", "rootNodes", "findAllRootNodes", "drawGizmosForDrawer", "componentsToProcess", "rootNode", "findComponentsRecursive", "length", "drawerName", "component", "node", "drawGizmos", "error", "results", "distance", "worldPosition", "subtract", "maxDrawDistance", "components", "getComponents", "canHandle", "push", "child", "children", "scene", "registerDrawer", "key", "has", "warn", "set", "onRegister", "unregisterDrawer", "get", "onUnregister", "delete", "get<PERSON>rawer", "getAllDrawers", "getInstance", "refresh", "Map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;;AACvBC,MAAAA,M,UAAAA,M;;AACaC,MAAAA,wB,iBAAAA,wB;;;;;;;2FAEtB;;;OAGM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;AAEjD;AACA;AACA;AACA;;8BAGaQ,Y,WAFZH,OAAO,CAAC,cAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,qDADlB,MAEaC,YAFb,SAEkCP,SAFlC,CAE4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AASP;AATO;;AAYD;AAEvC;AAdwC,eAehCQ,QAfgC,GAeJ,IAfI;AAoBxC;AApBwC,eAqBhCC,WArBgC,GAqBV,CArBU;AAAA,eAsBhCC,cAtBgC,GAsBP,IAAI,EAtBG;AAAA;;AA2B9BC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACT,MAAD,IAAW,CAAC,KAAKU,cAArB,EAAqC,OADhB,CAGrB;;AACAL,UAAAA,YAAY,CAACM,QAAb,GAAwB,IAAxB,CAJqB,CAMrB;;AACA,eAAKL,QAAL,GAAgB,KAAKM,YAAL,CAAkBb,QAAlB,KAA+B,KAAKc,YAAL,CAAkBd,QAAlB,CAA/C,CAPqB,CASrB;;AACA,eAAKS,cAAL,GAAsB,IAAI,KAAKM,WAA/B,CAVqB,CAYrB;;AACAT,UAAAA,YAAY,CAACU,mBAAb;AAEAC,UAAAA,OAAO,CAACC,GAAR,CAAY,wDAAZ;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,cAAIb,YAAY,CAACM,QAAb,KAA0B,IAA9B,EAAoC;AAChCN,YAAAA,YAAY,CAACM,QAAb,GAAwB,IAAxB;AACH;AACJ;;AAESQ,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACtC,cAAI,CAACpB,MAAD,IAAW,CAAC,KAAKU,cAArB,EAAqC;AACrC,cAAI,CAAC,KAAKW,aAAN,IAAuB,CAAC,KAAKf,QAAjC,EAA2C,OAFL,CAItC;;AACA,eAAKC,WAAL,IAAoBa,SAApB;AACA,cAAI,KAAKb,WAAL,GAAmB,KAAKC,cAA5B,EAA4C;AAC5C,eAAKD,WAAL,GAAmB,CAAnB;AAEA,eAAKe,aAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,aAAa,GAAS;AAC1B,cAAI,CAAC,KAAKhB,QAAV,EACI,OAFsB,CAI1B;;AACA,eAAKA,QAAL,CAAciB,KAAd;AAEAP,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAA0BZ,YAAY,CAACmB,OAAb,CAAqBC,IAA3D,EAP0B,CAQ1B;;AACA,gBAAMC,aAAa,GAAGC,KAAK,CAACC,IAAN,CAAWvB,YAAY,CAACmB,OAAb,CAAqBK,MAArB,EAAX,EACjBC,MADiB,CACVC,MAAM,IAAIA,MAAM,CAACC,OADP,EAEjBC,IAFiB,CAEZ,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,WAAF,KAAkBD,CAAC,CAACC,WAAF,EAFhB,CAAtB,CAT0B,CAa1B;;AACA,gBAAMC,SAAS,GAAG,KAAKC,gBAAL,EAAlB,CAd0B,CAgB1B;;AACA,eAAK,MAAMP,MAAX,IAAqBL,aAArB,EAAoC;AAChC,iBAAKa,mBAAL,CAAyBR,MAAzB,EAAiCM,SAAjC;AACH;AACJ;AAED;AACJ;AACA;;;AACYE,QAAAA,mBAAmB,CAACR,MAAD,EAAsBM,SAAtB,EAA+C;AACtE,cAAI,CAAC,KAAK/B,QAAV,EAAoB;AAEpB,gBAAMkC,mBAA2D,GAAG,EAApE,CAHsE,CAKtE;;AACA,eAAK,MAAMC,QAAX,IAAuBJ,SAAvB,EAAkC;AAC9B,iBAAKK,uBAAL,CAA6BD,QAA7B,EAAuCV,MAAvC,EAA+CS,mBAA/C;AACH;;AAEDxB,UAAAA,OAAO,CAACC,GAAR,CAAY,yBAAyBuB,mBAAmB,CAACG,MAA7C,GAAsD,kBAAtD,GAA2EZ,MAAM,CAACa,UAA9F,EAVsE,CAWtE;;AACA,eAAK,MAAM;AAAEC,YAAAA,SAAF;AAAaC,YAAAA;AAAb,WAAX,IAAkCN,mBAAlC,EAAuD;AACnD,gBAAI;AACAT,cAAAA,MAAM,CAACgB,UAAP,CAAkBF,SAAlB,EAA6B,KAAKvC,QAAlC,EAA4CwC,IAA5C;AACH,aAFD,CAEE,OAAOE,KAAP,EAAc;AACZhC,cAAAA,OAAO,CAACgC,KAAR,CAAe,0CAAyCjB,MAAM,CAACa,UAAW,GAA1E,EAA8EI,KAA9E;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYN,QAAAA,uBAAuB,CAACI,IAAD,EAAaf,MAAb,EAAkCkB,OAAlC,EAAyF;AACpH;AACA,gBAAMC,QAAQ,GAAGJ,IAAI,CAACK,aAAL,CAAmBC,QAAnB,CAA4B,KAAKN,IAAL,CAAUK,aAAtC,EAAqDR,MAArD,EAAjB;AACA,cAAIO,QAAQ,GAAG,KAAKG,eAApB,EAAqC,OAH+E,CAKpH;;AACA,gBAAMC,UAAU,GAAGR,IAAI,CAACS,aAAL,CAAmBzD,SAAnB,CAAnB;;AACA,eAAK,MAAM+C,SAAX,IAAwBS,UAAxB,EAAoC;AAChC,gBAAIvB,MAAM,CAACyB,SAAP,CAAiBX,SAAjB,CAAJ,EAAiC;AAC7BI,cAAAA,OAAO,CAACQ,IAAR,CAAa;AAAEZ,gBAAAA,SAAF;AAAaC,gBAAAA;AAAb,eAAb;AACH;AACJ,WAXmH,CAapH;;;AACA,eAAK,MAAMY,KAAX,IAAoBZ,IAAI,CAACa,QAAzB,EAAmC;AAC/B,iBAAKjB,uBAAL,CAA6BgB,KAA7B,EAAoC3B,MAApC,EAA4CkB,OAA5C;AACH;AACJ;AAED;AACJ;AACA;;;AACYX,QAAAA,gBAAgB,GAAW;AAC/B,gBAAMsB,KAAK,GAAG,KAAKd,IAAL,CAAUc,KAAxB;AACA,cAAI,CAACA,KAAL,EAAY,OAAO,EAAP;AAEZ,iBAAOA,KAAK,CAACD,QAAN,CAAe7B,MAAf,CAAsB4B,KAAK,IAAIA,KAAK,KAAK,KAAKZ,IAA9C,CAAP;AACH;AAED;AACJ;AACA;;;AACgC,eAAde,cAAc,CAAC9B,MAAD,EAA4B;AACpD,gBAAM+B,GAAG,GAAG/B,MAAM,CAACa,UAAnB;;AAEA,cAAIvC,YAAY,CAACmB,OAAb,CAAqBuC,GAArB,CAAyBD,GAAzB,CAAJ,EAAmC;AAC/B9C,YAAAA,OAAO,CAACgD,IAAR,CAAc,wBAAuBF,GAAI,wBAAzC;AACA;AACH;;AAEDzD,UAAAA,YAAY,CAACmB,OAAb,CAAqByC,GAArB,CAAyBH,GAAzB,EAA8B/B,MAA9B;AACAA,UAAAA,MAAM,CAACmC,UAAP;;AAEA,cAAIlE,MAAJ,EAAY;AACRgB,YAAAA,OAAO,CAACC,GAAR,CAAa,mCAAkC6C,GAAI,EAAnD;AACH;AACJ;AAED;AACJ;AACA;;;AACkC,eAAhBK,gBAAgB,CAACvB,UAAD,EAA8B;AACxD,gBAAMb,MAAM,GAAG1B,YAAY,CAACmB,OAAb,CAAqB4C,GAArB,CAAyBxB,UAAzB,CAAf;AACA,cAAI,CAACb,MAAL,EAAa,OAAO,KAAP;AAEbA,UAAAA,MAAM,CAACsC,YAAP;AACAhE,UAAAA,YAAY,CAACmB,OAAb,CAAqB8C,MAArB,CAA4B1B,UAA5B;;AAEA,cAAI5C,MAAJ,EAAY;AACRgB,YAAAA,OAAO,CAACC,GAAR,CAAa,qCAAoC2B,UAAW,EAA5D;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AAC2B,eAAT2B,SAAS,CAAC3B,UAAD,EAAyC;AAC5D,iBAAOvC,YAAY,CAACmB,OAAb,CAAqB4C,GAArB,CAAyBxB,UAAzB,KAAwC,IAA/C;AACH;AAED;AACJ;AACA;;;AAC+B,eAAb4B,aAAa,GAAkB;AACzC,iBAAO7C,KAAK,CAACC,IAAN,CAAWvB,YAAY,CAACmB,OAAb,CAAqBK,MAArB,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AAC6B,eAAX4C,WAAW,GAAwB;AAC7C,iBAAOpE,YAAY,CAACM,QAApB;AACH;AAED;AACJ;AACA;;;AACqC,eAAnBI,mBAAmB,GAAS;AACtC;AAAA;AAAA,oEAA0BgB,MAAD,IAAyB;AAC9C1B,YAAAA,YAAY,CAACwD,cAAb,CAA4B9B,MAA5B;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACyB,eAAP2C,OAAO,GAAS;AAC1B,gBAAM/D,QAAQ,GAAGN,YAAY,CAACoE,WAAb,EAAjB;;AACA,cAAI9D,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACW,aAAT;AACH;AACJ;;AA1NuC,O,UAkBzBE,O,GAAoC,IAAImD,GAAJ,E,UAOpChE,Q,GAAgC,I,0FAvB9CR,Q;;;;;iBAC+B,I;;yFAE/BA,Q;;;;;iBACgC,K;;sFAEhCA,Q;;;;;iBAC4B,E;;0FAE5BA,Q;;;;;iBACgC,I", "sourcesContent": ["import { _decorator, Component, Graphics, Node, find, Color } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { GizmoDrawer, autoRegisterGizmoDrawers } from './GizmoDrawer';\n\n// Import gizmo classes to ensure their decorators are executed\nimport './EmitterArcGizmo';\n\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n/**\n * Global gizmo manager that handles all gizmo drawing\n * Should be attached to a global node in the scene\n */\n@ccclass('GizmoManager')\n@executeInEditMode(true)\nexport class GizmoManager extends Component {\n    \n    @property\n    public gizmosEnabled: boolean = true;\n    \n    @property\n    public drawInPlayMode: boolean = false;\n    \n    @property\n    public refreshRate: number = 60; // FPS for gizmo updates\n    \n    @property\n    public maxDrawDistance: number = 2000; // Maximum distance to draw gizmos\n    \n    // Graphics component for drawing\n    private graphics: Graphics | null = null;\n    \n    // Registered gizmo drawers\n    private static drawers: Map<string, GizmoDrawer> = new Map();\n    \n    // Update timer\n    private updateTimer: number = 0;\n    private updateInterval: number = 1 / 60; // Default 60 FPS\n    \n    // Singleton instance\n    private static instance: GizmoManager | null = null;\n    \n    protected onLoad(): void {\n        if (!EDITOR && !this.drawInPlayMode) return;\n\n        // Set as singleton instance\n        GizmoManager.instance = this;\n\n        // Get or create Graphics component\n        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\n\n        // Update refresh interval\n        this.updateInterval = 1 / this.refreshRate;\n\n        // Auto-register all decorated gizmo drawers\n        GizmoManager.autoRegisterDrawers();\n\n        console.log('GizmoManager: Initialized with auto-registered drawers');\n    }\n    \n    protected onDestroy(): void {\n        if (GizmoManager.instance === this) {\n            GizmoManager.instance = null;\n        }\n    }\n    \n    protected update(deltaTime: number): void {\n        if (!EDITOR && !this.drawInPlayMode) return;\n        if (!this.gizmosEnabled || !this.graphics) return;\n        \n        // Throttle updates based on refresh rate\n        this.updateTimer += deltaTime;\n        if (this.updateTimer < this.updateInterval) return;\n        this.updateTimer = 0;\n        \n        this.drawAllGizmos();\n    }\n    \n    /**\n     * Draw all gizmos for all registered drawers\n     */\n    private drawAllGizmos(): void {\n        if (!this.graphics) \n            return;\n        \n        // Clear previous drawings\n        this.graphics.clear();\n        \n        console.log('GizmoManager: update ' + GizmoManager.drawers.size);\n        // Get all drawers sorted by priority\n        const sortedDrawers = Array.from(GizmoManager.drawers.values())\n            .filter(drawer => drawer.enabled)\n            .sort((a, b) => a.getPriority() - b.getPriority());\n        \n        // Find all nodes in the scene\n        const rootNodes = this.findAllRootNodes();\n        \n        // Draw gizmos for each drawer\n        for (const drawer of sortedDrawers) {\n            this.drawGizmosForDrawer(drawer, rootNodes);\n        }\n    }\n    \n    /**\n     * Draw gizmos for a specific drawer\n     */\n    private drawGizmosForDrawer(drawer: GizmoDrawer, rootNodes: Node[]): void {\n        if (!this.graphics) return;\n        \n        const componentsToProcess: { component: Component, node: Node }[] = [];\n        \n        // Find all components of the drawer's type\n        for (const rootNode of rootNodes) {\n            this.findComponentsRecursive(rootNode, drawer, componentsToProcess);\n        }\n        \n        console.log('GizmoManager: Found ' + componentsToProcess.length + ' components for ' + drawer.drawerName);\n        // Draw gizmos for each component\n        for (const { component, node } of componentsToProcess) {\n            try {\n                drawer.drawGizmos(component, this.graphics, node);\n            } catch (error) {\n                console.error(`GizmoManager: Error drawing gizmos for ${drawer.drawerName}:`, error);\n            }\n        }\n    }\n    \n    /**\n     * Recursively find components that match the drawer's type\n     */\n    private findComponentsRecursive(node: Node, drawer: GizmoDrawer, results: { component: Component, node: Node }[]): void {\n        // Check distance from gizmo manager\n        const distance = node.worldPosition.subtract(this.node.worldPosition).length();\n        if (distance > this.maxDrawDistance) return;\n        \n        // Check components on this node\n        const components = node.getComponents(Component);\n        for (const component of components) {\n            if (drawer.canHandle(component)) {\n                results.push({ component, node });\n            }\n        }\n        \n        // Recursively check children\n        for (const child of node.children) {\n            this.findComponentsRecursive(child, drawer, results);\n        }\n    }\n    \n    /**\n     * Find all root nodes in the scene\n     */\n    private findAllRootNodes(): Node[] {\n        const scene = this.node.scene;\n        if (!scene) return [];\n        \n        return scene.children.filter(child => child !== this.node);\n    }\n    \n    /**\n     * Register a gizmo drawer\n     */\n    public static registerDrawer(drawer: GizmoDrawer): void {\n        const key = drawer.drawerName;\n        \n        if (GizmoManager.drawers.has(key)) {\n            console.warn(`GizmoManager: Drawer ${key} is already registered`);\n            return;\n        }\n        \n        GizmoManager.drawers.set(key, drawer);\n        drawer.onRegister();\n        \n        if (EDITOR) {\n            console.log(`GizmoManager: Registered drawer ${key}`);\n        }\n    }\n    \n    /**\n     * Unregister a gizmo drawer\n     */\n    public static unregisterDrawer(drawerName: string): boolean {\n        const drawer = GizmoManager.drawers.get(drawerName);\n        if (!drawer) return false;\n        \n        drawer.onUnregister();\n        GizmoManager.drawers.delete(drawerName);\n        \n        if (EDITOR) {\n            console.log(`GizmoManager: Unregistered drawer ${drawerName}`);\n        }\n        \n        return true;\n    }\n    \n    /**\n     * Get a registered drawer by name\n     */\n    public static getDrawer(drawerName: string): GizmoDrawer | null {\n        return GizmoManager.drawers.get(drawerName) || null;\n    }\n    \n    /**\n     * Get all registered drawers\n     */\n    public static getAllDrawers(): GizmoDrawer[] {\n        return Array.from(GizmoManager.drawers.values());\n    }\n    \n    /**\n     * Get the singleton instance\n     */\n    public static getInstance(): GizmoManager | null {\n        return GizmoManager.instance;\n    }\n    \n    /**\n     * Auto-register all decorated gizmo drawers\n     */\n    public static autoRegisterDrawers(): void {\n        autoRegisterGizmoDrawers((drawer: GizmoDrawer) => {\n            GizmoManager.registerDrawer(drawer);\n        });\n    }\n\n    /**\n     * Force refresh all gizmos\n     */\n    public static refresh(): void {\n        const instance = GizmoManager.getInstance();\n        if (instance) {\n            instance.drawAllGizmos();\n        }\n    }\n}\n"]}