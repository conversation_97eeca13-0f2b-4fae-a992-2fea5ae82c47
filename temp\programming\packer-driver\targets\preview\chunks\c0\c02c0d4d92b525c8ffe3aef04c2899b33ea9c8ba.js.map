{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"], "names": ["Color", "Vec3", "GizmoDrawer", "RegisterGizmoDrawer", "Gizmo<PERSON><PERSON>s", "EmitterArc", "EmitterArcGizmo", "componentType", "drawerName", "showRadius", "showDirections", "showCenter", "showArc", "radiusColor", "GRAY", "directionColor", "RED", "centerColor", "WHITE", "arcColor", "YELLOW", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "gizmoPos", "worldToGizmoSpace", "worldPosition", "gizmoX", "x", "gizmoY", "y", "drawCenter", "radius", "drawRadius", "arc", "drawArcIndicator", "count", "drawDirections", "worldPos", "gizmoNode", "localPos", "inverseTransformPoint", "worldX", "worldY", "drawCross", "drawCircle", "strokeColor", "lineWidth", "baseAngleRad", "angle", "Math", "PI", "arcRad", "startAngle", "endAngle", "arcRadius", "segments", "max", "floor", "i", "cos", "sin", "moveTo", "lineTo", "startX", "startY", "endX", "endY", "stroke", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "direction", "getDirection", "spawnPos", "getSpawnPosition", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+BA,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;;AACnCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AACbC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;AACA;iCAEaC,e;;oEADb,MACaA,eADb;AAAA;AAAA,sCAC6D;AAAA;AAAA;AAAA,eAEzCC,aAFyC;AAAA;AAAA;AAAA,eAGzCC,UAHyC,GAG5B,iBAH4B;AAKzD;AALyD,eAMlDC,UANkD,GAM5B,IAN4B;AAAA,eAOlDC,cAPkD,GAOxB,IAPwB;AAAA,eAQlDC,UARkD,GAQ5B,IAR4B;AAAA,eASlDC,OATkD,GAS/B,IAT+B;AAWzD;AAXyD,eAYlDC,WAZkD,GAY7Bb,KAAK,CAACc,IAZuB;AAAA,eAalDC,cAbkD,GAa1Bf,KAAK,CAACgB,GAboB;AAAA,eAclDC,WAdkD,GAc7BjB,KAAK,CAACkB,KAduB;AAAA,eAelDC,QAfkD,GAehCnB,KAAK,CAACoB,MAf0B;AAiBzD;AAjByD,eAkBlDC,UAlBkD,GAkB7B,GAlB6B;AAAA,eAmBlDC,SAnBkD,GAmB9B,CAnB8B;AAAA,eAoBlDC,UApBkD,GAoB7B,CApB6B;AAAA;;AAsBlDC,QAAAA,UAAU,CAACC,OAAD,EAAsBC,QAAtB,EAA0CC,IAA1C,EAA4D;AACzE;AACA,cAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBF,IAAI,CAACG,aAA5B,EAA2CJ,QAAQ,CAACC,IAApD,CAAjB;AACA,cAAMI,MAAM,GAAGH,QAAQ,CAACI,CAAxB;AACA,cAAMC,MAAM,GAAGL,QAAQ,CAACM,CAAxB,CAJyE,CAMzE;;AACA,cAAI,KAAKvB,UAAT,EAAqB;AACjB,iBAAKwB,UAAL,CAAgBT,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC;AACH,WATwE,CAWzE;;;AACA,cAAI,KAAKxB,UAAL,IAAmBgB,OAAO,CAACW,MAAR,GAAiB,CAAxC,EAA2C;AACvC,iBAAKC,UAAL,CAAgBX,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC,EAA0CR,OAAO,CAACW,MAAlD;AACH,WAdwE,CAgBzE;;;AACA,cAAI,KAAKxB,OAAL,IAAgBa,OAAO,CAACa,GAAR,GAAc,CAAlC,EAAqC;AACjC,iBAAKC,gBAAL,CAAsBb,QAAtB,EAAgCD,OAAhC,EAAyCM,MAAzC,EAAiDE,MAAjD;AACH,WAnBwE,CAqBzE;;;AACA,cAAI,KAAKvB,cAAL,IAAuBe,OAAO,CAACe,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBf,QAApB,EAA8BD,OAA9B,EAAuCM,MAAvC,EAA+CE,MAA/C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYJ,QAAAA,iBAAiB,CAACa,QAAD,EAA2BC,SAA3B,EAAsE;AAC3F;AACA,cAAMC,QAAQ,GAAG,IAAI3C,IAAJ,EAAjB;AACA0C,UAAAA,SAAS,CAACE,qBAAV,CAAgCD,QAAhC,EAA0CF,QAA1C;AACA,iBAAO;AAAEV,YAAAA,CAAC,EAAEY,QAAQ,CAACZ,CAAd;AAAiBE,YAAAA,CAAC,EAAEU,QAAQ,CAACV;AAA7B,WAAP;AACH;;AAEOC,QAAAA,UAAU,CAACT,QAAD,EAAqBoB,MAArB,EAAqCC,MAArC,EAA2D;AACzE;AAAA;AAAA,wCAAWC,SAAX,CAAqBtB,QAArB,EAA+BoB,MAA/B,EAAuCC,MAAvC,EAA+C,KAAKxB,UAApD,EAAgE,KAAKN,WAArE;AACH;;AAEOoB,QAAAA,UAAU,CAACX,QAAD,EAAqBoB,MAArB,EAAqCC,MAArC,EAAqDX,MAArD,EAA2E;AACzF;AAAA;AAAA,wCAAWa,UAAX,CAAsBvB,QAAtB,EAAgCoB,MAAhC,EAAwCC,MAAxC,EAAgDX,MAAhD,EAAwD,KAAKvB,WAA7D,EAA0E,KAA1E;AACH;;AAEO0B,QAAAA,gBAAgB,CAACb,QAAD,EAAqBD,OAArB,EAA0CqB,MAA1C,EAA0DC,MAA1D,EAAgF;AACpG,cAAItB,OAAO,CAACa,GAAR,IAAe,CAAnB,EAAsB;AAEtBZ,UAAAA,QAAQ,CAACwB,WAAT,GAAuB,KAAK/B,QAA5B;AACAO,UAAAA,QAAQ,CAACyB,SAAT,GAAqB,CAArB,CAJoG,CAMpG;AACA;;AACA,cAAMC,YAAY,GAAG3B,OAAO,CAAC4B,KAAR,GAAgBC,IAAI,CAACC,EAArB,GAA0B,GAA/C;AACA,cAAMC,MAAM,GAAG/B,OAAO,CAACa,GAAR,GAAcgB,IAAI,CAACC,EAAnB,GAAwB,GAAvC;AAEA,cAAME,UAAU,GAAGL,YAAY,GAAGI,MAAM,GAAG,CAA3C;AACA,cAAME,QAAQ,GAAGN,YAAY,GAAGI,MAAM,GAAG,CAAzC,CAZoG,CAcpG;AACA;;AACA,cAAMG,SAAS,GAAGlC,OAAO,CAACW,MAAR,GAAiB,CAAjB,GAAqBX,OAAO,CAACW,MAA7B,GAAsC,EAAxD,CAhBoG,CAkBpG;;AACA,cAAMwB,QAAQ,GAAGN,IAAI,CAACO,GAAL,CAAS,CAAT,EAAYP,IAAI,CAACQ,KAAL,CAAWrC,OAAO,CAACa,GAAR,GAAc,CAAzB,CAAZ,CAAjB,CAnBoG,CAmBzC;;AAC3D,eAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIH,QAArB,EAA+BG,CAAC,EAAhC,EAAoC;AAChC,gBAAMV,KAAK,GAAGI,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,KAA2BM,CAAC,GAAGH,QAA/B,CAA3B;AACA,gBAAM5B,CAAC,GAAGc,MAAM,GAAGQ,IAAI,CAACU,GAAL,CAASX,KAAT,IAAkBM,SAArC;AACA,gBAAMzB,CAAC,GAAGa,MAAM,GAAGO,IAAI,CAACW,GAAL,CAASZ,KAAT,IAAkBM,SAArC;;AAEA,gBAAII,CAAC,KAAK,CAAV,EAAa;AACTrC,cAAAA,QAAQ,CAACwC,MAAT,CAAgBlC,CAAhB,EAAmBE,CAAnB;AACH,aAFD,MAEO;AACHR,cAAAA,QAAQ,CAACyC,MAAT,CAAgBnC,CAAhB,EAAmBE,CAAnB;AACH;AACJ,WA9BmG,CAgCpG;;;AACA,cAAMkC,MAAM,GAAGtB,MAAM,GAAGQ,IAAI,CAACU,GAAL,CAASP,UAAT,IAAuBE,SAA/C;AACA,cAAMU,MAAM,GAAGtB,MAAM,GAAGO,IAAI,CAACW,GAAL,CAASR,UAAT,IAAuBE,SAA/C;AACA,cAAMW,IAAI,GAAGxB,MAAM,GAAGQ,IAAI,CAACU,GAAL,CAASN,QAAT,IAAqBC,SAA3C;AACA,cAAMY,IAAI,GAAGxB,MAAM,GAAGO,IAAI,CAACW,GAAL,CAASP,QAAT,IAAqBC,SAA3C,CApCoG,CAsCpG;;AACAjC,UAAAA,QAAQ,CAACwC,MAAT,CAAgBpB,MAAhB,EAAwBC,MAAxB;AACArB,UAAAA,QAAQ,CAACyC,MAAT,CAAgBC,MAAhB,EAAwBC,MAAxB;AACA3C,UAAAA,QAAQ,CAACwC,MAAT,CAAgBpB,MAAhB,EAAwBC,MAAxB;AACArB,UAAAA,QAAQ,CAACyC,MAAT,CAAgBG,IAAhB,EAAsBC,IAAtB;AAEA7C,UAAAA,QAAQ,CAAC8C,MAAT;AACH;;AAEO/B,QAAAA,cAAc,CAACf,QAAD,EAAqBD,OAArB,EAA0CqB,MAA1C,EAA0DC,MAA1D,EAAgF;AAClG,cAAM0B,UAAU,GAAG,EAAnB;AACA,cAAMC,WAAW,GAAGjD,OAAO,CAACkD,eAAR,IAA2B,CAA/C;AACA,cAAMC,WAAW,GAAGtB,IAAI,CAACO,GAAL,CAASY,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKrD,UAArD,CAApB;;AAEA,eAAK,IAAI0C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtC,OAAO,CAACe,KAA5B,EAAmCuB,CAAC,EAApC,EAAwC;AACpC,gBAAMc,SAAS,GAAGpD,OAAO,CAACqD,YAAR,CAAqBf,CAArB,CAAlB;AACA,gBAAMgB,QAAQ,GAAGtD,OAAO,CAACuD,gBAAR,CAAyBjB,CAAzB,CAAjB,CAFoC,CAIpC;;AACA,gBAAMK,MAAM,GAAGtB,MAAM,GAAGiC,QAAQ,CAAC/C,CAAjC;AACA,gBAAMqC,MAAM,GAAGtB,MAAM,GAAGgC,QAAQ,CAAC7C,CAAjC,CANoC,CAQpC;;AACA,gBAAMoC,IAAI,GAAGF,MAAM,GAAGS,SAAS,CAAC7C,CAAV,GAAc4C,WAApC;AACA,gBAAML,IAAI,GAAGF,MAAM,GAAGQ,SAAS,CAAC3C,CAAV,GAAc0C,WAApC,CAVoC,CAYpC;;AACA;AAAA;AAAA,0CAAWK,SAAX,CAAqBvD,QAArB,EAA+B0C,MAA/B,EAAuCC,MAAvC,EAA+CC,IAA/C,EAAqDC,IAArD,EAA2D,KAAKxD,cAAhE,EAAgF,KAAKO,SAArF;AACH;AACJ;;AAEM4D,QAAAA,WAAW,GAAW;AACzB,iBAAO,EAAP,CADyB,CACd;AACd;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAYP;AACL,cAAIA,OAAO,CAAC3E,UAAR,KAAuB4E,SAA3B,EAAsC,KAAK5E,UAAL,GAAkB2E,OAAO,CAAC3E,UAA1B;AACtC,cAAI2E,OAAO,CAAC1E,cAAR,KAA2B2E,SAA/B,EAA0C,KAAK3E,cAAL,GAAsB0E,OAAO,CAAC1E,cAA9B;AAC1C,cAAI0E,OAAO,CAACzE,UAAR,KAAuB0E,SAA3B,EAAsC,KAAK1E,UAAL,GAAkByE,OAAO,CAACzE,UAA1B;AACtC,cAAIyE,OAAO,CAACxE,OAAR,KAAoByE,SAAxB,EAAmC,KAAKzE,OAAL,GAAewE,OAAO,CAACxE,OAAvB;AACnC,cAAIwE,OAAO,CAACvE,WAAR,KAAwBwE,SAA5B,EAAuC,KAAKxE,WAAL,GAAmBuE,OAAO,CAACvE,WAA3B;AACvC,cAAIuE,OAAO,CAACrE,cAAR,KAA2BsE,SAA/B,EAA0C,KAAKtE,cAAL,GAAsBqE,OAAO,CAACrE,cAA9B;AAC1C,cAAIqE,OAAO,CAACnE,WAAR,KAAwBoE,SAA5B,EAAuC,KAAKpE,WAAL,GAAmBmE,OAAO,CAACnE,WAA3B;AACvC,cAAImE,OAAO,CAACjE,QAAR,KAAqBkE,SAAzB,EAAoC,KAAKlE,QAAL,GAAgBiE,OAAO,CAACjE,QAAxB;AACpC,cAAIiE,OAAO,CAAC/D,UAAR,KAAuBgE,SAA3B,EAAsC,KAAKhE,UAAL,GAAkB+D,OAAO,CAAC/D,UAA1B;AACtC,cAAI+D,OAAO,CAAC9D,SAAR,KAAsB+D,SAA1B,EAAqC,KAAK/D,SAAL,GAAiB8D,OAAO,CAAC9D,SAAzB;AACrC,cAAI8D,OAAO,CAAC7D,UAAR,KAAuB8D,SAA3B,EAAsC,KAAK9D,UAAL,GAAkB6D,OAAO,CAAC7D,UAA1B;AACzC;;AAxKwD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\nimport { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';\nimport { GizmoUtils } from './GizmoUtils';\nimport { EmitterArc } from '../world/bullet/EmitterArc';\n\n/**\n * Gizmo drawer for EmitterArc components\n * Draws visual debugging information for arc-based bullet emitters\n */\n@RegisterGizmoDrawer\nexport class EmitterArcGizmo extends GizmoDrawer<EmitterArc> {\n    \n    public readonly componentType = EmitterArc;\n    public readonly drawerName = \"EmitterArcGizmo\";\n    \n    // Gizmo display options\n    public showRadius: boolean = true;\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    public showArc: boolean = true;\n    \n    // Colors\n    public radiusColor: Color = Color.GRAY;\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    public arcColor: Color = Color.YELLOW;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: EmitterArc, graphics: Graphics, node: Node): void {\n        // For 2D projects, convert world position to graphics local space\n        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);\n        const gizmoX = gizmoPos.x;\n        const gizmoY = gizmoPos.y;\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics, gizmoX, gizmoY);\n        }\n\n        // Draw radius circle\n        if (this.showRadius && emitter.radius > 0) {\n            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius);\n        }\n\n        // Draw arc indicator\n        if (this.showArc && emitter.arc > 0) {\n            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY);\n        }\n\n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter, gizmoX, gizmoY);\n        }\n    }\n\n    /**\n     * Convert world position to gizmo graphics coordinate space\n     * For 2D projects, this converts world coordinates to the local space of the graphics node\n     */\n    private worldToGizmoSpace(worldPos: Readonly<Vec3>, gizmoNode: Node): { x: number, y: number } {\n        // Convert world position to local position of the gizmo graphics node\n        const localPos = new Vec3();\n        gizmoNode.inverseTransformPoint(localPos, worldPos);\n        return { x: localPos.x, y: localPos.y };\n    }\n    \n    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {\n        GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n    }\n\n    private drawRadius(graphics: Graphics, worldX: number, worldY: number, radius: number): void {\n        GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);\n    }\n    \n    private drawArcIndicator(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        if (emitter.arc <= 0) return;\n\n        graphics.strokeColor = this.arcColor;\n        graphics.lineWidth = 2;\n\n        // Convert angle and arc to radians\n        // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset\n        const baseAngleRad = emitter.angle * Math.PI / 180;\n        const arcRad = emitter.arc * Math.PI / 180;\n\n        const startAngle = baseAngleRad - arcRad / 2;\n        const endAngle = baseAngleRad + arcRad / 2;\n\n        // Draw arc at radius distance\n        // Use emitter radius if > 0, otherwise use minimum radius for visibility\n        const arcRadius = emitter.radius > 0 ? emitter.radius : 30;\n\n        // Draw arc\n        const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs\n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + (endAngle - startAngle) * (i / segments);\n            const x = worldX + Math.cos(angle) * arcRadius;\n            const y = worldY + Math.sin(angle) * arcRadius;\n\n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n\n        // Draw arc end indicators\n        const startX = worldX + Math.cos(startAngle) * arcRadius;\n        const startY = worldY + Math.sin(startAngle) * arcRadius;\n        const endX = worldX + Math.cos(endAngle) * arcRadius;\n        const endY = worldY + Math.sin(endAngle) * arcRadius;\n\n        // Lines from center to arc ends\n        graphics.moveTo(worldX, worldY);\n        graphics.lineTo(startX, startY);\n        graphics.moveTo(worldX, worldY);\n        graphics.lineTo(endX, endY);\n\n        graphics.stroke();\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < emitter.count; i++) {\n            const direction = emitter.getDirection(i);\n            const spawnPos = emitter.getSpawnPosition(i);\n\n            // Start position (at spawn position relative to world position)\n            const startX = worldX + spawnPos.x;\n            const startY = worldY + spawnPos.y;\n\n            // End position (direction from spawn position)\n            const endX = startX + direction.x * arrowLength;\n            const endY = startY + direction.y * arrowLength;\n\n            // Draw arrow\n            GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 10; // Draw emitter gizmos with medium priority\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showRadius?: boolean;\n        showDirections?: boolean;\n        showCenter?: boolean;\n        showArc?: boolean;\n        radiusColor?: Color;\n        directionColor?: Color;\n        centerColor?: Color;\n        arcColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showRadius !== undefined) this.showRadius = options.showRadius;\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.showArc !== undefined) this.showArc = options.showArc;\n        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.arcColor !== undefined) this.arcColor = options.arcColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}