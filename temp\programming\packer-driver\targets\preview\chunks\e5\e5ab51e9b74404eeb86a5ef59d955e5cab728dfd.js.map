{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"], "names": ["GizmoDrawer", "RegisterGizmoDrawer", "constructor", "gizmoDrawerRegistry", "push", "EDITOR", "console", "log", "name", "length", "getRegisteredGizmoDrawers", "autoRegisterGizmoDrawers", "registerFunction", "DrawerConstructor", "drawer", "drawerName", "error", "warn", "Gizmo<PERSON><PERSON>s", "enabled", "canHandle", "component", "componentType", "onRegister", "onUnregister", "getPriority", "drawCross", "graphics", "x", "y", "size", "color", "drawCircle", "radius", "filled", "drawArrow", "startX", "startY", "endX", "endY", "arrowSize", "drawLine", "lineWidth", "drawRect", "width", "height", "drawText", "_graphics", "text", "_color"], "mappings": ";;;mGAyDsBA,W;;AAlDtB;AACA;AACA;AACA;AACO,WAASC,mBAAT,CAA8DC,WAA9D,EAAiF;AACpF;AACAC,IAAAA,mBAAmB,CAACC,IAApB,CAAyBF,WAAzB;;AACA,QAAIG,MAAJ,EAAY;AACRC,MAAAA,OAAO,CAACC,GAAR,8BAAuCL,WAAW,CAACM,IAAnD,uCAAyFL,mBAAmB,CAACM,MAA7G;AACH;;AAED,WAAOP,WAAP;AACH;AAED;AACA;AACA;;;AACO,WAASQ,yBAAT,GAAmE;AACtE,WAAO,CAAC,GAAGP,mBAAJ,CAAP;AACH;AAED;AACA;AACA;AACA;;;AACO,WAASQ,wBAAT,CAAkCC,gBAAlC,EAAyF;AAC5FN,IAAAA,OAAO,CAACC,GAAR,mDAA4DJ,mBAAmB,CAACM,MAAhF;;AAEA,SAAK,IAAMI,iBAAX,IAAgCV,mBAAhC,EAAqD;AACjD,UAAI;AACAG,QAAAA,OAAO,CAACC,GAAR,wCAAiDM,iBAAiB,CAACL,IAAnE;;AACA,YAAMM,OAAM,GAAG,IAAID,iBAAJ,EAAf;;AACAD,QAAAA,gBAAgB,CAACE,OAAD,CAAhB;AACAR,QAAAA,OAAO,CAACC,GAAR,2CAAoDO,OAAM,CAACC,UAA3D;AACH,OALD,CAKE,OAAOC,KAAP,EAAc;AACZV,QAAAA,OAAO,CAACU,KAAR,2CAAsDH,iBAAiB,CAACL,IAAxE,QAAiFQ,KAAjF;AACH;AACJ;;AAED,QAAIX,MAAM,IAAIF,mBAAmB,CAACM,MAApB,GAA6B,CAA3C,EAA8C;AAC1CH,MAAAA,OAAO,CAACC,GAAR,mCAA4CJ,mBAAmB,CAACM,MAAhE;AACH,KAFD,MAEO,IAAIN,mBAAmB,CAACM,MAApB,KAA+B,CAAnC,EAAsC;AACzCH,MAAAA,OAAO,CAACW,IAAR;AACH;AACJ;AAED;AACA;AACA;AACA;;;;;;;;yBA7CgBhB,mB;+BAaAS,yB;8BAQAC,wB;;;;;;;;;;;;AA/BPN,MAAAA,M,UAAAA,M;;AACAa,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACMf,MAAAA,mB,GAAoD,E;;6BAoDpCH,W,GAAf,MAAeA,WAAf,CAA4D;AAAA;AAE/D;AACJ;AACA;;AAGI;AACJ;AACA;;AAGI;AACJ;AACA;AAdmE,eAexDmB,OAfwD,GAerC,IAfqC;AAAA;AAiB/D;AACJ;AACA;AACA;AACA;AACA;;;AAGI;AACJ;AACA;AACA;AACA;AACWC,QAAAA,SAAS,CAACC,SAAD,EAAuC;AACnD,iBAAOA,SAAS,YAAY,KAAKC,aAAjC;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,GAAS;AACtB,cAAIlB,MAAJ,EAAY;AACRC,YAAAA,OAAO,CAACC,GAAR,8BAAuC,KAAKQ,UAA5C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWS,QAAAA,YAAY,GAAS;AACxB,cAAInB,MAAJ,EAAY;AACRC,YAAAA,OAAO,CAACC,GAAR,gCAAyC,KAAKQ,UAA9C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWU,QAAAA,WAAW,GAAW;AACzB,iBAAO,CAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACC,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2CC,IAA3C,EAAyDC,KAAzD,EAA6E;AAC5F;AAAA;AAAA,wCAAWL,SAAX,CAAqBC,QAArB,EAA+BC,CAA/B,EAAkCC,CAAlC,EAAqCC,IAArC,EAA2CC,KAA3C;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,CAACL,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2CI,MAA3C,EAA2DF,KAA3D,EAAyEG,MAAzE,EAAwG;AAAA,cAA/BA,MAA+B;AAA/BA,YAAAA,MAA+B,GAAb,KAAa;AAAA;;AACxH;AAAA;AAAA,wCAAWF,UAAX,CAAsBL,QAAtB,EAAgCC,CAAhC,EAAmCC,CAAnC,EAAsCI,MAAtC,EAA8CF,KAA9C,EAAqDG,MAArD;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACR,QAAD,EAAqBS,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFR,KAAjF,EAA+FS,SAA/F,EAA4H;AAAA,cAA7BA,SAA6B;AAA7BA,YAAAA,SAA6B,GAAT,CAAS;AAAA;;AAC3I;AAAA;AAAA,wCAAWL,SAAX,CAAqBR,QAArB,EAA+BS,MAA/B,EAAuCC,MAAvC,EAA+CC,IAA/C,EAAqDC,IAArD,EAA2DR,KAA3D,EAAkES,SAAlE;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAACd,QAAD,EAAqBS,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFR,KAAjF,EAA+FW,SAA/F,EAA4H;AAAA,cAA7BA,SAA6B;AAA7BA,YAAAA,SAA6B,GAAT,CAAS;AAAA;;AAC1I;AAAA;AAAA,wCAAWD,QAAX,CAAoBd,QAApB,EAA8BS,MAA9B,EAAsCC,MAAtC,EAA8CC,IAA9C,EAAoDC,IAApD,EAA0DR,KAA1D,EAAiEW,SAAjE;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAAChB,QAAD,EAAqBC,CAArB,EAAgCC,CAAhC,EAA2Ce,KAA3C,EAA0DC,MAA1D,EAA0Ed,KAA1E,EAAwFG,MAAxF,EAAuH;AAAA,cAA/BA,MAA+B;AAA/BA,YAAAA,MAA+B,GAAb,KAAa;AAAA;;AACrI;AAAA;AAAA,wCAAWS,QAAX,CAAoBhB,QAApB,EAA8BC,CAA9B,EAAiCC,CAAjC,EAAoCe,KAApC,EAA2CC,MAA3C,EAAmDd,KAAnD,EAA0DG,MAA1D;AACH;AAED;AACJ;AACA;AACA;;;AACcY,QAAAA,QAAQ,CAACC,SAAD,EAAsBC,IAAtB,EAAoCpB,CAApC,EAA+CC,CAA/C,EAA0DoB,MAA1D,EAA+E;AAC7F;AACA;AACA,cAAI5C,MAAJ,EAAY;AACRC,YAAAA,OAAO,CAACC,GAAR,qBAA8BqB,CAA9B,UAAoCC,CAApC,WAA2CmB,IAA3C;AACH;AACJ;;AA3G8D,O", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Node } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { GizmoUtils } from './GizmoUtils';\n\n// Registry for auto-registration\nconst gizmoDrawerRegistry: Array<new () => GizmoDrawer> = [];\n\n/**\n * Decorator to automatically register a gizmo drawer\n * Usage: @RegisterGizmoDrawer class MyGizmo extends GizmoDrawer { ... }\n */\nexport function RegisterGizmoDrawer<T extends new () => GizmoDrawer>(constructor: T): T {\n    // Add to registry for auto-registration\n    gizmoDrawerRegistry.push(constructor);\n    if (EDITOR) {\n        console.log(`GizmoDrawer: Registered ${constructor.name} for auto-registration (total: ${gizmoDrawerRegistry.length})`);\n    }\n\n    return constructor;\n}\n\n/**\n * Get all registered gizmo drawer constructors\n */\nexport function getRegisteredGizmoDrawers(): Array<new () => GizmoDrawer> {\n    return [...gizmoDrawerRegistry];\n}\n\n/**\n * Auto-register all decorated gizmo drawers to a GizmoManager\n * This function should be called from GizmoManager to avoid circular dependencies\n */\nexport function autoRegisterGizmoDrawers(registerFunction: (drawer: GizmoDrawer) => void): void {\n    console.log(`GizmoDrawer: Starting auto-registration with ${gizmoDrawerRegistry.length} registered drawer types`);\n\n    for (const DrawerConstructor of gizmoDrawerRegistry) {\n        try {\n            console.log(`GizmoDrawer: Creating instance of ${DrawerConstructor.name}`);\n            const drawer = new DrawerConstructor();\n            registerFunction(drawer);\n            console.log(`GizmoDrawer: Successfully registered ${drawer.drawerName}`);\n        } catch (error) {\n            console.error(`Failed to auto-register gizmo drawer ${DrawerConstructor.name}:`, error);\n        }\n    }\n\n    if (EDITOR && gizmoDrawerRegistry.length > 0) {\n        console.log(`GizmoDrawer: Auto-registered ${gizmoDrawerRegistry.length} gizmo drawers`);\n    } else if (gizmoDrawerRegistry.length === 0) {\n        console.warn(`GizmoDrawer: No gizmo drawers found in registry! Make sure classes are decorated with @RegisterGizmoDrawer`);\n    }\n}\n\n/**\n * Abstract base class for drawing gizmos for specific component types\n * This is not a component itself, but a drawer that can be registered to GizmoManager\n */\nexport abstract class GizmoDrawer<T extends Component = Component> {\n    \n    /**\n     * The component type this drawer handles\n     */\n    public abstract readonly componentType: new (...args: any[]) => T;\n    \n    /**\n     * Name of this gizmo drawer for debugging\n     */\n    public abstract readonly drawerName: string;\n    \n    /**\n     * Whether this drawer is enabled\n     */\n    public enabled: boolean = true;\n    \n    /**\n     * Draw gizmos for the given component\n     * @param component The component to draw gizmos for\n     * @param graphics The graphics component to draw with\n     * @param node The node that contains the component\n     */\n    public abstract drawGizmos(component: T, graphics: Graphics, node: Node): void;\n    \n    /**\n     * Check if this drawer can handle the given component\n     * @param component The component to check\n     * @returns true if this drawer can handle the component\n     */\n    public canHandle(component: Component): component is T {\n        return component instanceof this.componentType;\n    }\n    \n    /**\n     * Called when the drawer is registered to the manager\n     * Override this to perform any initialization\n     */\n    public onRegister(): void {\n        if (EDITOR) {\n            console.log(`GizmoDrawer: Registered ${this.drawerName}`);\n        }\n    }\n    \n    /**\n     * Called when the drawer is unregistered from the manager\n     * Override this to perform any cleanup\n     */\n    public onUnregister(): void {\n        if (EDITOR) {\n            console.log(`GizmoDrawer: Unregistered ${this.drawerName}`);\n        }\n    }\n    \n    /**\n     * Get the priority of this drawer (higher priority draws last/on top)\n     * Override this to change drawing order\n     */\n    public getPriority(): number {\n        return 0;\n    }\n    \n    /**\n     * Helper method to draw a cross at the given position\n     */\n    protected drawCross(graphics: Graphics, x: number, y: number, size: number, color: Color): void {\n        GizmoUtils.drawCross(graphics, x, y, size, color);\n    }\n\n    /**\n     * Helper method to draw a circle\n     */\n    protected drawCircle(graphics: Graphics, x: number, y: number, radius: number, color: Color, filled: boolean = false): void {\n        GizmoUtils.drawCircle(graphics, x, y, radius, color, filled);\n    }\n\n    /**\n     * Helper method to draw an arrow\n     */\n    protected drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8): void {\n        GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, color, arrowSize);\n    }\n\n    /**\n     * Helper method to draw a line\n     */\n    protected drawLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, lineWidth: number = 1): void {\n        GizmoUtils.drawLine(graphics, startX, startY, endX, endY, color, lineWidth);\n    }\n\n    /**\n     * Helper method to draw a rectangle\n     */\n    protected drawRect(graphics: Graphics, x: number, y: number, width: number, height: number, color: Color, filled: boolean = false): void {\n        GizmoUtils.drawRect(graphics, x, y, width, height, color, filled);\n    }\n    \n    /**\n     * Helper method to draw text (simple implementation)\n     * Note: For more complex text rendering, consider using Label components\n     */\n    protected drawText(_graphics: Graphics, text: string, x: number, y: number, _color: Color): void {\n        // This is a placeholder - in a real implementation you might want to use Label components\n        // or a more sophisticated text rendering system\n        if (EDITOR) {\n            console.log(`Gizmo Text at (${x}, ${y}): ${text}`);\n        }\n    }\n}\n"]}